package handler

import (
	"net/http"
	"strconv"

	v1 "go-abm-idp/api/v1"
	"go-abm-idp/internal/service"

	"github.com/gin-gonic/gin"
)

type OAuth2AdminHandler struct {
	*Handler
	oauth2AdminService service.OAuth2AdminService
}

func NewOAuth2AdminHandler(handler *Handler, oauth2AdminService service.OAuth2AdminService) *OAuth2AdminHandler {
	return &OAuth2AdminHandler{
		Handler:            handler,
		oauth2AdminService: oauth2AdminService,
	}
}

// GetDashboard godoc
// @Summary 获取OAuth2仪表板数
// @Schemes
// @Description 获取OAuth2管理仪表板的统计数据
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} v1.GetOAuth2DashboardResponse
// @Router /admin/oauth2/dashboard [get]
func (h *OAuth2AdminHandler) GetDashboard(ctx *gin.Context) {
	data, err := h.oauth2AdminService.GetDashboard(ctx)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, data)
}

// GetProviders godoc
// @Summary 获取OAuth2 Provider列表
// @Schemes
// @Description 获取OAuth2 Provider列表,支持分页和搜索
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param name query string false "Provider名称"
// @Param clientType query string false "客户端类

// @Param status query string false "状

// @Success 200 {object} v1.GetOAuth2ProvidersResponse
// @Router /admin/oauth2/providers [get]
func (h *OAuth2AdminHandler) GetProviders(ctx *gin.Context) {
	var req v1.GetOAuth2ProvidersRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	data, err := h.oauth2AdminService.GetProviders(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, data)
}

// CreateProvider godoc
// @Summary 创建OAuth2 Provider
// @Schemes
// @Description 创建新的OAuth2 Provider
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.CreateOAuth2ProviderRequest true "参数"
// @Success 200 {object} v1.Response
// @Router /admin/oauth2/providers [post]
func (h *OAuth2AdminHandler) CreateProvider(ctx *gin.Context) {
	var req v1.CreateOAuth2ProviderRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	err := h.oauth2AdminService.CreateProvider(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// GetProvider godoc
// @Summary 获取OAuth2 Provider详情
// @Schemes
// @Description 获取指定OAuth2 Provider的详细信
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path string true "Provider ID"
// @Success 200 {object} v1.GetOAuth2ProviderResponse
// @Router /admin/oauth2/providers/{id} [get]
func (h *OAuth2AdminHandler) GetProvider(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	data, err := h.oauth2AdminService.GetProvider(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, data)
}

// UpdateProvider godoc
// @Summary 更新OAuth2 Provider
// @Schemes
// @Description 更新OAuth2 Provider信息
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path string true "Provider ID"
// @Param request body v1.UpdateOAuth2ProviderRequest true "参数"
// @Success 200 {object} v1.Response
// @Router /admin/oauth2/providers/{id} [put]
func (h *OAuth2AdminHandler) UpdateProvider(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.UpdateOAuth2ProviderRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}
	req.ID = uint(id)

	err = h.oauth2AdminService.UpdateProvider(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// DeleteProvider godoc
// @Summary 删除OAuth2 Provider
// @Schemes
// @Description 删除指定的OAuth2 Provider
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path string true "Provider ID"
// @Success 200 {object} v1.Response
// @Router /admin/oauth2/providers/{id} [delete]
func (h *OAuth2AdminHandler) DeleteProvider(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	err = h.oauth2AdminService.DeleteProvider(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// BatchDeleteProviders godoc
// @Summary 批量删除OAuth2提供
// @Schemes
// @Description 批量删除多个OAuth2提供
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteProvidersRequest true "参数"
// @Success 200 {object} v1.Response
// @Router /admin/oauth2/providers/batch [delete]
func (h *OAuth2AdminHandler) BatchDeleteProviders(ctx *gin.Context) {
	var req v1.BatchDeleteProvidersRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	err := h.oauth2AdminService.BatchDeleteProviders(ctx, req.IDs)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// BatchImportProviders godoc
// @Summary 批量导入OAuth2提供
// @Schemes
// @Description 批量导入OAuth2提供者数
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body []v1.ImportProviderData true "参数"
// @Success 200 {object} v1.BatchImportResult
// @Router /admin/oauth2/providers/import [post]
func (h *OAuth2AdminHandler) BatchImportProviders(ctx *gin.Context) {
	var req []*v1.ImportProviderData
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	result, err := h.oauth2AdminService.BatchImportProviders(ctx, req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, result)
}

// ToggleProviderStatus godoc
// @Summary 切换OAuth2提供者状
// @Schemes
// @Description 启用或禁用OAuth2提供
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path uint true "提供者ID"
// @Param request body v1.ToggleProviderStatusRequest true "参数"
// @Success 200 {object} v1.Response
// @Router /admin/oauth2/providers/{id}/toggle-status [post]
func (h *OAuth2AdminHandler) ToggleProviderStatus(ctx *gin.Context) {
	id := ctx.Param("id")
	providerID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.ToggleProviderStatusRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	err = h.oauth2AdminService.ToggleProviderStatus(ctx, uint(providerID), req.Status)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// RegenerateSecret godoc
// @Summary 重新生成Provider密钥
// @Schemes
// @Description 重新生成OAuth2 Provider的客户端密钥
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path string true "Provider ID"
// @Success 200 {object} v1.RegenerateSecretResponse
// @Router /admin/oauth2/providers/{id}/regenerate-secret [post]
func (h *OAuth2AdminHandler) RegenerateSecret(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	data, err := h.oauth2AdminService.RegenerateSecret(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, data)
}

// SearchTokens godoc
// @Summary 搜索OAuth2令牌
// @Schemes
// @Description 搜索OAuth2令牌,支持多种筛选条
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param clientId query string false "客户端ID"
// @Param userId query string false "用户ID"
// @Param tokenType query string false "令牌类型"
// @Param status query string false "状

// @Success 200 {object} v1.SearchTokensResponse
// @Router /admin/oauth2/tokens/search [get]
func (h *OAuth2AdminHandler) SearchTokens(ctx *gin.Context) {
	var req v1.SearchTokensRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	data, err := h.oauth2AdminService.SearchTokens(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, data)
}

// GetTokenAnalytics godoc
// @Summary 获取令牌分析数据
// @Schemes
// @Description 获取OAuth2令牌的统计分析数
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} v1.GetTokenAnalyticsResponse
// @Router /admin/oauth2/tokens/analytics [get]
func (h *OAuth2AdminHandler) GetTokenAnalytics(ctx *gin.Context) {
	data, err := h.oauth2AdminService.GetTokenAnalytics(ctx)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, data)
}

// RevokeToken godoc
// @Summary 撤销OAuth2令牌
// @Schemes
// @Description 撤销指定的OAuth2令牌
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param tokenId path string true "令牌ID"
// @Success 200 {object} v1.Response
// @Router /admin/oauth2/tokens/{tokenId}/revoke [post]
func (h *OAuth2AdminHandler) RevokeToken(ctx *gin.Context) {
	tokenId := ctx.Param("tokenId")
	if tokenId == "" {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	err := h.oauth2AdminService.RevokeToken(ctx, tokenId)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// ExtendTokenExpiry godoc
// @Summary 延长令牌有效
// @Schemes
// @Description 延长OAuth2令牌的有效期
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param tokenId path string true "令牌ID"
// @Param request body v1.ExtendTokenExpiryRequest true "参数"
// @Success 200 {object} v1.Response
// @Router /admin/oauth2/tokens/{tokenId}/extend [post]
func (h *OAuth2AdminHandler) ExtendTokenExpiry(ctx *gin.Context) {
	tokenId := ctx.Param("tokenId")
	if tokenId == "" {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.ExtendTokenExpiryRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	err := h.oauth2AdminService.ExtendTokenExpiry(ctx, tokenId, req.Duration)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// CleanupExpiredTokens godoc
// @Summary 清理过期令牌
// @Schemes
// @Description 清理所有过期的OAuth2令牌
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} v1.CleanupTokensResponse
// @Router /admin/oauth2/tokens/cleanup [post]
func (h *OAuth2AdminHandler) CleanupExpiredTokens(ctx *gin.Context) {
	data, err := h.oauth2AdminService.CleanupExpiredTokens(ctx)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, data)
}

// SearchUserAuthorizations godoc
// @Summary 搜索用户授权
// @Schemes
// @Description 搜索用户授权,支持多种筛选条
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param userEmail query string false "用户邮箱"
// @Param clientId query string false "客户端ID"
// @Param status query string false "状

// @Success 200 {object} v1.SearchUserAuthorizationsResponse
// @Router /admin/oauth2/authorizations/search [get]
func (h *OAuth2AdminHandler) SearchUserAuthorizations(ctx *gin.Context) {
	var req v1.SearchUserAuthorizationsRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	data, err := h.oauth2AdminService.SearchUserAuthorizations(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, data)
}

// GetAuthorizationAnalytics godoc
// @Summary 获取授权分析数据
// @Schemes
// @Description 获取用户授权的统计分析数
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} v1.GetAuthorizationAnalyticsResponse
// @Router /admin/oauth2/authorizations/analytics [get]
func (h *OAuth2AdminHandler) GetAuthorizationAnalytics(ctx *gin.Context) {
	data, err := h.oauth2AdminService.GetAuthorizationAnalytics(ctx)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, data)
}

// RevokeUserAuthorization godoc
// @Summary 撤销用户授权
// @Schemes
// @Description 撤销指定的用户授
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param authorizationId path string true "授权ID"
// @Success 200 {object} v1.Response
// @Router /admin/oauth2/authorizations/{authorizationId}/revoke [post]
func (h *OAuth2AdminHandler) RevokeUserAuthorization(ctx *gin.Context) {
	authorizationId := ctx.Param("authorizationId")
	if authorizationId == "" {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	err := h.oauth2AdminService.RevokeUserAuthorization(ctx, authorizationId)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// BatchRevokeUserAuthorizations godoc
// @Summary 批量撤销用户授权
// @Schemes
// @Description 批量撤销用户授权
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchRevokeAuthorizationsRequest true "参数"
// @Success 200 {object} v1.Response
// @Router /admin/oauth2/authorizations/batch [post]
func (h *OAuth2AdminHandler) BatchRevokeUserAuthorizations(ctx *gin.Context) {
	var req v1.BatchRevokeAuthorizationsRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	err := h.oauth2AdminService.BatchRevokeUserAuthorizations(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// SearchAuditLogs godoc
// @Summary 搜索审计日志
// @Schemes
// @Description 搜索审计日志,支持多种筛选条
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param level query string false "日志级别"
// @Param category query string false "分类"
// @Param userEmail query string false "用户邮箱"
// @Param clientId query string false "客户端ID"
// @Success 200 {object} v1.SearchAuditLogsResponse
// @Router /admin/oauth2/audit/logs/search [get]
func (h *OAuth2AdminHandler) SearchAuditLogs(ctx *gin.Context) {
	var req v1.SearchAuditLogsRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	data, err := h.oauth2AdminService.SearchAuditLogs(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, data)
}

// GetAuditLogAnalytics godoc
// @Summary 获取审计日志分析数据
// @Schemes
// @Description 获取审计日志的统计分析数
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} v1.GetAuditLogAnalyticsResponse
// @Router /admin/oauth2/audit/logs/analytics [get]
func (h *OAuth2AdminHandler) GetAuditLogAnalytics(ctx *gin.Context) {
	data, err := h.oauth2AdminService.GetAuditLogAnalytics(ctx)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, data)
}

// CreateAuditLog godoc
// @Summary 创建审计日志
// @Schemes
// @Description 创建新的审计日志记录
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.CreateAuditLogRequest true "参数"
// @Success 200 {object} v1.Response
// @Router /admin/oauth2/audit/logs [post]
func (h *OAuth2AdminHandler) CreateAuditLog(ctx *gin.Context) {
	var req v1.CreateAuditLogRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	err := h.oauth2AdminService.CreateAuditLog(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// DeleteOldAuditLogs godoc
// @Summary 清理旧审计日
// @Schemes
// @Description 删除指定天数之前的审计日
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.DeleteOldAuditLogsRequest true "参数"
// @Success 200 {object} v1.Response
// @Router /admin/oauth2/audit/logs/cleanup [post]
func (h *OAuth2AdminHandler) DeleteOldAuditLogs(ctx *gin.Context) {
	var req v1.DeleteOldAuditLogsRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	err := h.oauth2AdminService.DeleteOldAuditLogs(ctx, req.RetentionDays)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// GetGlobalSettings godoc
// @Summary 获取全局设置
// @Schemes
// @Description 获取OAuth2系统的全局设置
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} v1.GetGlobalSettingsResponse
// @Router /admin/oauth2/settings/global [get]
func (h *OAuth2AdminHandler) GetGlobalSettings(ctx *gin.Context) {
	data, err := h.oauth2AdminService.GetGlobalSettings(ctx)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, data)
}

// UpdateGlobalSettings godoc
// @Summary 更新全局设置
// @Schemes
// @Description 更新OAuth2系统的全局设置
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.UpdateGlobalSettingsRequest true "参数"
// @Success 200 {object} v1.Response
// @Router /admin/oauth2/settings/global [put]
func (h *OAuth2AdminHandler) UpdateGlobalSettings(ctx *gin.Context) {
	var req v1.UpdateGlobalSettingsRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	err := h.oauth2AdminService.UpdateGlobalSettings(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// GetSecuritySettings godoc
// @Summary 获取安全设置
// @Schemes
// @Description 获取OAuth2系统的安全设
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} v1.GetSecuritySettingsResponse
// @Router /admin/oauth2/settings/security [get]
func (h *OAuth2AdminHandler) GetSecuritySettings(ctx *gin.Context) {
	data, err := h.oauth2AdminService.GetSecuritySettings(ctx)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, data)
}

// UpdateSecuritySettings godoc
// @Summary 更新安全设置
// @Schemes
// @Description 更新OAuth2系统的安全设
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.UpdateSecuritySettingsRequest true "参数"
// @Success 200 {object} v1.Response
// @Router /admin/oauth2/settings/security [put]
func (h *OAuth2AdminHandler) UpdateSecuritySettings(ctx *gin.Context) {
	var req v1.UpdateSecuritySettingsRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	err := h.oauth2AdminService.UpdateSecuritySettings(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// GetSystemInfo godoc
// @Summary 获取系统信息
// @Schemes
// @Description 获取OAuth2系统的详细信
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} v1.GetSystemInfoResponse
// @Router /admin/oauth2/settings/system/info [get]
func (h *OAuth2AdminHandler) GetSystemInfo(ctx *gin.Context) {
	data, err := h.oauth2AdminService.GetSystemInfo(ctx)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, data)
}

// GetSystemHealth godoc
// @Summary 获取系统健康状
// @Schemes
// @Description 获取OAuth2系统的健康检查状
// @Tags OAuth2管理
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} v1.GetSystemHealthResponse
// @Router /admin/oauth2/settings/system/health [get]
func (h *OAuth2AdminHandler) GetSystemHealth(ctx *gin.Context) {
	data, err := h.oauth2AdminService.GetSystemHealth(ctx)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}
	v1.HandleSuccess(ctx, data)
}
