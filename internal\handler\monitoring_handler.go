package handler

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"go-abm-idp/internal/monitoring"
)

// MonitoringHandler 监控处理
type MonitoringHandler struct {
	logger          *zap.Logger
	keyUsageMonitor *monitoring.KeyUsageMonitor
	rotationMetrics *monitoring.RotationMetrics
	healthChecker   *monitoring.RotationHealthChecker
	alertManager    *monitoring.AlertManager
}

// NewMonitoringHandler 创建监控处理
func NewMonitoringHandler(
	logger *zap.Logger,
	keyUsageMonitor *monitoring.KeyUsageMonitor,
	rotationMetrics *monitoring.RotationMetrics,
	healthChecker *monitoring.RotationHealthChecker,
	alertManager *monitoring.AlertManager,
) *MonitoringHandler {
	return &MonitoringHandler{
		logger:          logger,
		keyUsageMonitor: keyUsageMonitor,
		rotationMetrics: rotationMetrics,
		healthChecker:   healthChecker,
		alertManager:    alertManager,
	}
}

// GetKeyUsageStats 获取密钥使用统计
func (mh *MonitoringHandler) GetKeyUsageStats(c *gin.Context) {
	keyID := c.Param("keyId")
	if keyID == "" {
		// 获取所有密钥统
		allStats := mh.keyUsageMonitor.GetAllKeyStats()
		c.JSON(http.StatusOK, gin.H{
			"code":    http.StatusOK,
			"message": "success",
			"data":    allStats,
		})
		return
	}

	// 获取特定密钥统计
	stats, exists := mh.keyUsageMonitor.GetKeyUsageStats(keyID)
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    http.StatusNotFound,
			"message": "Key not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "success",
		"data":    stats,
	})
}

// GetHealthStatus 获取健康状
func (mh *MonitoringHandler) GetHealthStatus(c *gin.Context) {
	ctx := c.Request.Context()
	healthChecks := mh.healthChecker.GetAllHealthChecks(ctx)

	// 计算整体健康状
	overallStatus := "healthy"
	for _, check := range healthChecks {
		if check.Status == "unhealthy" {
			overallStatus = "unhealthy"
			break
		} else if check.Status == "warning" && overallStatus == "healthy" {
			overallStatus = "warning"
		}
	}

	response := map[string]interface{}{
		"overall_status": overallStatus,
		"timestamp":      time.Now(),
		"checks":         healthChecks,
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "success",
		"data":    response,
	})
}

// GetMetricsSummary 获取指标摘要
func (mh *MonitoringHandler) GetMetricsSummary(c *gin.Context) {
	// 获取时间范围参数
	hoursStr := c.DefaultQuery("hours", "24")
	hours, err := strconv.Atoi(hoursStr)
	if err != nil || hours <= 0 {
		hours = 24
	}

	// 构建指标摘要
	summary := map[string]interface{}{
		"time_range": fmt.Sprintf("%d hours", hours),
		"timestamp":  time.Now(),
		"key_usage":  mh.getKeyUsageSummary(hours),
		"rotation":   mh.getRotationSummary(hours),
		"security":   mh.getSecuritySummary(hours),
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "success",
		"data":    summary,
	})
}

// getKeyUsageSummary 获取密钥使用摘要
func (mh *MonitoringHandler) getKeyUsageSummary(hours int) map[string]interface{} {
	allStats := mh.keyUsageMonitor.GetAllKeyStats()

	totalOperations := int64(0)
	totalErrors := int64(0)
	activeKeys := 0

	for _, stats := range allStats {
		totalOperations += stats.TotalOperations
		totalErrors += stats.ErrorCount
		if stats.LastUsed.After(time.Now().Add(-time.Duration(hours) * time.Hour)) {
			activeKeys++
		}
	}

	errorRate := float64(0)
	if totalOperations > 0 {
		errorRate = float64(totalErrors) / float64(totalOperations)
	}

	return map[string]interface{}{
		"total_operations": totalOperations,
		"total_errors":     totalErrors,
		"error_rate":       errorRate,
		"active_keys":      activeKeys,
		"total_keys":       len(allStats),
	}
}

// getRotationSummary 获取轮换摘要
func (mh *MonitoringHandler) getRotationSummary(hours int) map[string]interface{} {
	// TODO: 从数据库获取轮换统计
	return map[string]interface{}{
		"total_rotations":      0,
		"successful_rotations": 0,
		"failed_rotations":     0,
		"pending_rotations":    0,
	}
}

// getSecuritySummary 获取安全摘要
func (mh *MonitoringHandler) getSecuritySummary(hours int) map[string]interface{} {
	// TODO: 从安全日志获取统
	return map[string]interface{}{
		"security_events":       0,
		"suspicious_activities": 0,
		"auth_failures":         0,
		"blocked_requests":      0,
	}
}

// GetKeyUsageChart 获取密钥使用图表数据
func (mh *MonitoringHandler) GetKeyUsageChart(c *gin.Context) {
	keyID := c.Param("keyId")
	chartType := c.DefaultQuery("type", "hourly") // hourly, daily, weekly

	stats, exists := mh.keyUsageMonitor.GetKeyUsageStats(keyID)
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    http.StatusNotFound,
			"message": "Key not found",
		})
		return
	}

	var chartData interface{}

	switch chartType {
	case "hourly":
		chartData = mh.buildHourlyChart(stats)
	case "daily":
		chartData = mh.buildDailyChart(stats)
	case "operations":
		chartData = mh.buildOperationsChart(stats)
	case "clients":
		chartData = mh.buildClientsChart(stats)
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "Invalid chart type",
		})
		return
	}

	response := map[string]interface{}{
		"key_id":     keyID,
		"chart_type": chartType,
		"data":       chartData,
		"timestamp":  time.Now(),
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "success",
		"data":    response,
	})
}

// buildHourlyChart 构建小时图表数据
func (mh *MonitoringHandler) buildHourlyChart(stats *monitoring.KeyUsageStats) map[string]interface{} {
	labels := make([]string, 24)
	data := make([]int64, 24)

	for i := 0; i < 24; i++ {
		labels[i] = fmt.Sprintf("%02d:00", i)
		data[i] = stats.HourlyStats[i]
	}

	return map[string]interface{}{
		"labels": labels,
		"datasets": []map[string]interface{}{
			{
				"label":           "Operations per Hour",
				"data":            data,
				"backgroundColor": "rgba(54, 162, 235, 0.2)",
				"borderColor":     "rgba(54, 162, 235, 1)",
				"borderWidth":     1,
			},
		},
	}
}

// buildDailyChart 构建日图表数
func (mh *MonitoringHandler) buildDailyChart(stats *monitoring.KeyUsageStats) map[string]interface{} {
	labels := []string{"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"}
	data := make([]int64, 7)

	for i := 0; i < 7; i++ {
		data[i] = stats.DailyStats[i]
	}

	return map[string]interface{}{
		"labels": labels,
		"datasets": []map[string]interface{}{
			{
				"label":           "Operations per Day",
				"data":            data,
				"backgroundColor": "rgba(255, 99, 132, 0.2)",
				"borderColor":     "rgba(255, 99, 132, 1)",
				"borderWidth":     1,
			},
		},
	}
}

// buildOperationsChart 构建操作类型图表数据
func (mh *MonitoringHandler) buildOperationsChart(stats *monitoring.KeyUsageStats) map[string]interface{} {
	labels := make([]string, 0, len(stats.OperationTypes))
	data := make([]int64, 0, len(stats.OperationTypes))

	for operation, count := range stats.OperationTypes {
		labels = append(labels, operation)
		data = append(data, count)
	}

	return map[string]interface{}{
		"labels": labels,
		"datasets": []map[string]interface{}{
			{
				"label": "Operations by Type",
				"data":  data,
				"backgroundColor": []string{
					"rgba(255, 99, 132, 0.2)",
					"rgba(54, 162, 235, 0.2)",
					"rgba(255, 205, 86, 0.2)",
					"rgba(75, 192, 192, 0.2)",
					"rgba(153, 102, 255, 0.2)",
				},
				"borderColor": []string{
					"rgba(255, 99, 132, 1)",
					"rgba(54, 162, 235, 1)",
					"rgba(255, 205, 86, 1)",
					"rgba(75, 192, 192, 1)",
					"rgba(153, 102, 255, 1)",
				},
				"borderWidth": 1,
			},
		},
	}
}

// buildClientsChart 构建客户端图表数
func (mh *MonitoringHandler) buildClientsChart(stats *monitoring.KeyUsageStats) map[string]interface{} {
	labels := make([]string, 0, len(stats.ClientStats))
	data := make([]int64, 0, len(stats.ClientStats))

	for client, count := range stats.ClientStats {
		labels = append(labels, client)
		data = append(data, count)
	}

	return map[string]interface{}{
		"labels": labels,
		"datasets": []map[string]interface{}{
			{
				"label":           "Operations by Client",
				"data":            data,
				"backgroundColor": "rgba(75, 192, 192, 0.2)",
				"borderColor":     "rgba(75, 192, 192, 1)",
				"borderWidth":     1,
			},
		},
	}
}

// SendTestAlert 发送测试告
func (mh *MonitoringHandler) SendTestAlert(c *gin.Context) {
	var request struct {
		Title    string                 `json:"title" binding:"required"`
		Message  string                 `json:"message" binding:"required"`
		Level    string                 `json:"level" binding:"required"`
		Labels   map[string]string      `json:"labels"`
		Metadata map[string]interface{} `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "Invalid request",
			"data":    err.Error(),
		})
		return
	}

	// 验证告警级别
	var level monitoring.AlertLevel
	switch request.Level {
	case "info":
		level = monitoring.AlertLevelInfo
	case "warning":
		level = monitoring.AlertLevelWarning
	case "error":
		level = monitoring.AlertLevelError
	case "critical":
		level = monitoring.AlertLevelCritical
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "Invalid alert level",
		})
		return
	}

	alert := &monitoring.Alert{
		Title:    request.Title,
		Message:  request.Message,
		Level:    level,
		Source:   "test",
		Labels:   request.Labels,
		Metadata: request.Metadata,
	}

	if err := mh.alertManager.SendAlert(alert); err != nil {
		mh.logger.Error("Failed to send test alert", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "Failed to send alert",
			"data":    err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "success",
		"data": map[string]interface{}{
			"message":  "Test alert sent successfully",
			"alert_id": alert.ID,
		},
	})
}

// GetMonitoringDashboard 获取监控仪表板页
func (mh *MonitoringHandler) GetMonitoringDashboard(c *gin.Context) {
	// 返回监控仪表板HTML页面
	c.HTML(http.StatusOK, "monitoring_dashboard.html", gin.H{
		"title":     "OAuth2 Key Management Monitoring",
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
	})
}
