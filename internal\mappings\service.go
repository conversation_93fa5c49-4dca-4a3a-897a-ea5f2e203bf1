package mappings

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// PropertyMappingService 属性映射服务
type PropertyMappingService struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewPropertyMappingService 创建新的属性映射服务
func NewPropertyMappingService(db *gorm.DB, logger *zap.Logger) *PropertyMappingService {
	return &PropertyMappingService{
		db:     db,
		logger: logger,
	}
}

// CreateMapping 创建属性映射
func (s *PropertyMappingService) CreateMapping(mapping PropertyMapping) error {
	if err := mapping.Validate(); err != nil {
		return fmt.Errorf("mapping validation failed: %w", err)
	}
	
	// 使用工厂保存映射
	factory := NewPropertyMappingFactory(s.db)
	return factory.SaveMapping(mapping)
}

// GetMappingByUUID 根据UUID获取属性映射
func (s *PropertyMappingService) GetMappingByUUID(mappingUUID uuid.UUID) (PropertyMapping, error) {
	factory := NewPropertyMappingFactory(s.db)
	return factory.LoadMapping(mappingUUID)
}

// GetMappingByName 根据名称获取属性映射
func (s *PropertyMappingService) GetMappingByName(name string) (PropertyMapping, error) {
	var baseMapping struct {
		MappingUUID uuid.UUID `gorm:"column:mapping_uuid"`
		MappingType string    `gorm:"column:mapping_type"`
		Name        string    `gorm:"column:name"`
	}
	
	if err := s.db.Table("property_mappings").Where("name = ?", name).First(&baseMapping).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("mapping not found: %s", name)
		}
		return nil, fmt.Errorf("failed to get mapping: %w", err)
	}
	
	factory := NewPropertyMappingFactory(s.db)
	return factory.LoadMapping(baseMapping.MappingUUID)
}

// ListMappings 列出所有属性映射
func (s *PropertyMappingService) ListMappings() ([]PropertyMapping, error) {
	var baseMappings []struct {
		MappingUUID uuid.UUID `gorm:"column:mapping_uuid"`
		MappingType string    `gorm:"column:mapping_type"`
		Name        string    `gorm:"column:name"`
	}
	
	if err := s.db.Table("property_mappings").Find(&baseMappings).Error; err != nil {
		return nil, fmt.Errorf("failed to list mappings: %w", err)
	}
	
	factory := NewPropertyMappingFactory(s.db)
	mappings := make([]PropertyMapping, 0, len(baseMappings))
	
	for _, baseMapping := range baseMappings {
		mapping, err := factory.LoadMapping(baseMapping.MappingUUID)
		if err != nil {
			s.logger.Warn("Failed to load mapping",
				zap.String("mapping_uuid", baseMapping.MappingUUID.String()),
				zap.Error(err))
			continue
		}
		mappings = append(mappings, mapping)
	}
	
	return mappings, nil
}

// ListMappingsByType 根据类型列出属性映射
func (s *PropertyMappingService) ListMappingsByType(mappingType PropertyMappingType) ([]PropertyMapping, error) {
	var baseMappings []struct {
		MappingUUID uuid.UUID `gorm:"column:mapping_uuid"`
		MappingType string    `gorm:"column:mapping_type"`
		Name        string    `gorm:"column:name"`
	}
	
	if err := s.db.Table("property_mappings").Where("mapping_type = ?", string(mappingType)).Find(&baseMappings).Error; err != nil {
		return nil, fmt.Errorf("failed to list mappings by type: %w", err)
	}
	
	factory := NewPropertyMappingFactory(s.db)
	mappings := make([]PropertyMapping, 0, len(baseMappings))
	
	for _, baseMapping := range baseMappings {
		mapping, err := factory.LoadMapping(baseMapping.MappingUUID)
		if err != nil {
			s.logger.Warn("Failed to load mapping",
				zap.String("mapping_uuid", baseMapping.MappingUUID.String()),
				zap.Error(err))
			continue
		}
		mappings = append(mappings, mapping)
	}
	
	return mappings, nil
}

// EvaluateMapping 评估属性映射
func (s *PropertyMappingService) EvaluateMapping(mappingUUID uuid.UUID, context map[string]interface{}) (interface{}, error) {
	mapping, err := s.GetMappingByUUID(mappingUUID)
	if err != nil {
		return nil, err
	}
	
	result, err := mapping.Evaluate(context)
	if err != nil {
		s.logger.Error("Failed to evaluate mapping",
			zap.String("mapping_uuid", mappingUUID.String()),
			zap.String("mapping_name", mapping.GetName()),
			zap.Error(err))
		return nil, fmt.Errorf("mapping evaluation failed: %w", err)
	}
	
	// 记录使用统计
	s.recordMappingUsage(mappingUUID, "evaluation")
	
	return result, nil
}

// EvaluateMappings 评估多个属性映射
func (s *PropertyMappingService) EvaluateMappings(mappingUUIDs []uuid.UUID, context map[string]interface{}) (map[string]interface{}, error) {
	results := make(map[string]interface{})
	
	for _, mappingUUID := range mappingUUIDs {
		mapping, err := s.GetMappingByUUID(mappingUUID)
		if err != nil {
			s.logger.Warn("Failed to get mapping for evaluation",
				zap.String("mapping_uuid", mappingUUID.String()),
				zap.Error(err))
			continue
		}
		
		result, err := mapping.Evaluate(context)
		if err != nil {
			s.logger.Error("Failed to evaluate mapping",
				zap.String("mapping_uuid", mappingUUID.String()),
				zap.String("mapping_name", mapping.GetName()),
				zap.Error(err))
			continue
		}
		
		// 将结果合并到总结果中
		if resultMap, ok := result.(map[string]interface{}); ok {
			for key, value := range resultMap {
				results[key] = value
			}
		} else {
			results[mapping.GetName()] = result
		}
		
		// 记录使用统计
		s.recordMappingUsage(mappingUUID, "evaluation")
	}
	
	return results, nil
}

// TestMapping 测试属性映射
func (s *PropertyMappingService) TestMapping(mappingUUID uuid.UUID, testInput map[string]interface{}) (*PropertyMappingTest, error) {
	mapping, err := s.GetMappingByUUID(mappingUUID)
	if err != nil {
		return nil, err
	}
	
	test := &PropertyMappingTest{
		TestUUID:    uuid.New(),
		MappingUUID: mappingUUID,
		TestInput:   testInput,
		CreatedAt:   time.Now(),
	}
	
	// 执行测试
	result, err := mapping.Evaluate(testInput)
	if err != nil {
		test.Success = false
		test.Error = err.Error()
	} else {
		test.Success = true
		if resultMap, ok := result.(map[string]interface{}); ok {
			test.TestOutput = resultMap
		} else {
			test.TestOutput = map[string]interface{}{"result": result}
		}
	}
	
	// 保存测试结果
	if err := s.db.Create(test).Error; err != nil {
		s.logger.Error("Failed to save mapping test result",
			zap.String("mapping_uuid", mappingUUID.String()),
			zap.Error(err))
	}
	
	return test, nil
}

// UpdateMapping 更新属性映射
func (s *PropertyMappingService) UpdateMapping(mapping PropertyMapping) error {
	if err := mapping.Validate(); err != nil {
		return fmt.Errorf("mapping validation failed: %w", err)
	}
	
	// 使用工厂更新映射
	factory := NewPropertyMappingFactory(s.db)
	return factory.UpdateMapping(mapping)
}

// DeleteMapping 删除属性映射
func (s *PropertyMappingService) DeleteMapping(mappingUUID uuid.UUID) error {
	// 检查映射是否被使用
	var usageCount int64
	if err := s.db.Model(&PropertyMappingUsage{}).Where("mapping_uuid = ?", mappingUUID).Count(&usageCount).Error; err != nil {
		return fmt.Errorf("failed to check mapping usage: %w", err)
	}
	
	if usageCount > 0 {
		return fmt.Errorf("mapping is still in use and cannot be deleted")
	}
	
	// 删除测试记录
	if err := s.db.Delete(&PropertyMappingTest{}, "mapping_uuid = ?", mappingUUID).Error; err != nil {
		return fmt.Errorf("failed to delete mapping tests: %w", err)
	}
	
	// 删除映射本身
	if err := s.db.Delete(&BasePropertyMapping{}, "mapping_uuid = ?", mappingUUID).Error; err != nil {
		return fmt.Errorf("failed to delete mapping: %w", err)
	}
	
	s.logger.Info("Property mapping deleted", zap.String("mapping_uuid", mappingUUID.String()))
	return nil
}

// RecordMappingUsage 记录映射使用
func (s *PropertyMappingService) RecordMappingUsage(mappingUUID uuid.UUID, objectType string, objectUUID uuid.UUID) error {
	// 查找现有使用记录
	var usage PropertyMappingUsage
	err := s.db.Where("mapping_uuid = ? AND object_type = ? AND object_uuid = ?", 
		mappingUUID, objectType, objectUUID).First(&usage).Error
	
	if err == gorm.ErrRecordNotFound {
		// 创建新的使用记录
		usage = PropertyMappingUsage{
			UsageUUID:   uuid.New(),
			MappingUUID: mappingUUID,
			ObjectType:  objectType,
			ObjectUUID:  objectUUID,
			UsageCount:  1,
			LastUsed:    time.Now(),
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}
		
		return s.db.Create(&usage).Error
	} else if err != nil {
		return fmt.Errorf("failed to query mapping usage: %w", err)
	}
	
	// 更新现有使用记录
	usage.UsageCount++
	usage.LastUsed = time.Now()
	usage.UpdatedAt = time.Now()
	
	return s.db.Save(&usage).Error
}

// recordMappingUsage 内部方法记录映射使用
func (s *PropertyMappingService) recordMappingUsage(mappingUUID uuid.UUID, action string) {
	// 简化的使用记录，实际应该记录更详细的信息
	s.logger.Debug("Mapping used",
		zap.String("mapping_uuid", mappingUUID.String()),
		zap.String("action", action))
}

// CreateDefaultMappings 创建默认属性映射
func (s *PropertyMappingService) CreateDefaultMappings() error {
	// 创建默认的Scope映射
	scopeMappings := []*ScopeMapping{
		{
			BasePropertyMapping: BasePropertyMapping{
				Name:       "authentik default OAuth Mapping: OpenID 'openid'",
				Expression: "return {'sub': user.username}",
			},
			ScopeName:   "openid",
			Description: "Default OpenID scope mapping",
		},
		{
			BasePropertyMapping: BasePropertyMapping{
				Name:       "authentik default OAuth Mapping: OpenID 'profile'",
				Expression: "return {'name': user.name, 'given_name': user.first_name, 'family_name': user.last_name}",
			},
			ScopeName:   "profile",
			Description: "Default profile scope mapping",
		},
		{
			BasePropertyMapping: BasePropertyMapping{
				Name:       "authentik default OAuth Mapping: OpenID 'email'",
				Expression: "return {'email': user.email, 'email_verified': user.email_verified}",
			},
			ScopeName:   "email",
			Description: "Default email scope mapping",
		},
	}
	
	for _, mapping := range scopeMappings {
		if err := s.CreateMapping(mapping); err != nil {
			s.logger.Warn("Failed to create default scope mapping",
				zap.String("name", mapping.Name),
				zap.Error(err))
		}
	}
	
	// 创建默认的SAML映射
	samlMappings := []*SAMLPropertyMapping{
		{
			BasePropertyMapping: BasePropertyMapping{
				Name:       "authentik default SAML Mapping: Username",
				Expression: "return user.username",
			},
			SAMLName:     "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name",
			FriendlyName: "Username",
		},
		{
			BasePropertyMapping: BasePropertyMapping{
				Name:       "authentik default SAML Mapping: Email",
				Expression: "return user.email",
			},
			SAMLName:     "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress",
			FriendlyName: "Email",
		},
	}
	
	for _, mapping := range samlMappings {
		if err := s.CreateMapping(mapping); err != nil {
			s.logger.Warn("Failed to create default SAML mapping",
				zap.String("name", mapping.Name),
				zap.Error(err))
		}
	}
	
	s.logger.Info("Default property mappings created successfully")
	return nil
}
