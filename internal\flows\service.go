package flows

import (
	"fmt"
	"time"

	"go-abm-idp/internal/events"
	"go-abm-idp/internal/policies"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// FlowService Flow管理服务
type FlowService struct {
	db            *gorm.DB
	logger        *zap.Logger
	policyService *policies.PolicyService
	eventService  *events.EventService
}

// NewFlowService 创建新的Flow服务
func NewFlowService(db *gorm.DB, logger *zap.Logger, policyService *policies.PolicyService, eventService *events.EventService) *FlowService {
	return &FlowService{
		db:            db,
		logger:        logger,
		policyService: policyService,
		eventService:  eventService,
	}
}

// CreateFlow 创建新的Flow
func (s *FlowService) CreateFlow(flow *Flow) error {
	if err := s.validateFlow(flow); err != nil {
		return fmt.Errorf("flow validation failed: %w", err)
	}

	flow.FlowUUID = uuid.New()
	flow.CreatedAt = time.Now()
	flow.UpdatedAt = time.Now()

	if err := s.db.Create(flow).Error; err != nil {
		return fmt.Errorf("failed to create flow: %w", err)
	}

	s.logger.Info("Flow created",
		zap.String("flow_uuid", flow.FlowUUID.String()),
		zap.String("flow_slug", flow.Slug),
		zap.String("flow_name", flow.Name))

	return nil
}

// GetFlowBySlug 根据Slug获取Flow
func (s *FlowService) GetFlowBySlug(slug string) (*Flow, error) {
	var flow Flow
	if err := s.db.Preload("Stages").Where("slug = ?", slug).First(&flow).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("flow not found: %s", slug)
		}
		return nil, fmt.Errorf("failed to get flow: %w", err)
	}

	return &flow, nil
}

// GetFlowByDesignation 根据用途获取Flow
func (s *FlowService) GetFlowByDesignation(designation FlowDesignation) (*Flow, error) {
	var flow Flow
	if err := s.db.Preload("Stages").Where("designation = ?", designation).First(&flow).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("flow not found for designation: %s", designation)
		}
		return nil, fmt.Errorf("failed to get flow: %w", err)
	}

	return &flow, nil
}

// ListFlows 列出所有Flow
func (s *FlowService) ListFlows() ([]Flow, error) {
	var flows []Flow
	if err := s.db.Preload("Stages").Find(&flows).Error; err != nil {
		return nil, fmt.Errorf("failed to list flows: %w", err)
	}

	return flows, nil
}

// UpdateFlow 更新Flow
func (s *FlowService) UpdateFlow(flow *Flow) error {
	if err := s.validateFlow(flow); err != nil {
		return fmt.Errorf("flow validation failed: %w", err)
	}

	flow.UpdatedAt = time.Now()

	if err := s.db.Save(flow).Error; err != nil {
		return fmt.Errorf("failed to update flow: %w", err)
	}

	s.logger.Info("Flow updated",
		zap.String("flow_uuid", flow.FlowUUID.String()),
		zap.String("flow_slug", flow.Slug))

	return nil
}

// DeleteFlow 删除Flow
func (s *FlowService) DeleteFlow(flowUUID uuid.UUID) error {
	if err := s.db.Delete(&Flow{}, "flow_uuid = ?", flowUUID).Error; err != nil {
		return fmt.Errorf("failed to delete flow: %w", err)
	}

	s.logger.Info("Flow deleted", zap.String("flow_uuid", flowUUID.String()))
	return nil
}

// AddStageToFlow 向Flow添加Stage
func (s *FlowService) AddStageToFlow(flowUUID, stageUUID uuid.UUID, order int) error {
	binding := &FlowStageBinding{
		BindingUUID:           uuid.New(),
		FlowUUID:              flowUUID,
		StageUUID:             stageUUID,
		Order:                 order,
		EvaluateOnPlan:        false,
		ReEvaluatePolicies:    true,
		PolicyEngineMode:      "any",
		InvalidResponseAction: InvalidResponseRetry,
		CreatedAt:             time.Now(),
		UpdatedAt:             time.Now(),
	}

	if err := s.db.Create(binding).Error; err != nil {
		return fmt.Errorf("failed to add stage to flow: %w", err)
	}

	s.logger.Info("Stage added to flow",
		zap.String("flow_uuid", flowUUID.String()),
		zap.String("stage_uuid", stageUUID.String()),
		zap.Int("order", order))

	return nil
}

// RemoveStageFromFlow 从Flow移除Stage
func (s *FlowService) RemoveStageFromFlow(flowUUID, stageUUID uuid.UUID) error {
	if err := s.db.Delete(&FlowStageBinding{}, "flow_uuid = ? AND stage_uuid = ?", flowUUID, stageUUID).Error; err != nil {
		return fmt.Errorf("failed to remove stage from flow: %w", err)
	}

	s.logger.Info("Stage removed from flow",
		zap.String("flow_uuid", flowUUID.String()),
		zap.String("stage_uuid", stageUUID.String()))

	return nil
}

// ExecuteFlow 执行Flow
func (s *FlowService) ExecuteFlow(flowSlug string, ctx *gin.Context, context map[string]interface{}) (*StageResult, error) {
	// 获取Flow
	flow, err := s.GetFlowBySlug(flowSlug)
	if err != nil {
		return nil, err
	}

	// 创建规划器
	planner := NewFlowPlanner(flow, s.logger, s.policyService, s.eventService)

	// 创建执行计划
	plan, err := planner.Plan(ctx, context)
	if err != nil {
		return nil, fmt.Errorf("failed to create flow plan: %w", err)
	}

	// 将计划存储到会话
	session := ctx.MustGet("session").(map[string]interface{})
	session[SessionKeyPlan] = plan

	// 创建执行器
	executor := NewFlowExecutorView(flow, plan, ctx, s.logger, s.policyService)

	// 执行Flow
	result, err := executor.Execute()
	if err != nil {
		return nil, fmt.Errorf("failed to execute flow: %w", err)
	}

	return result, nil
}

// ContinueFlow 继续执行Flow
func (s *FlowService) ContinueFlow(ctx *gin.Context) (*StageResult, error) {
	// 从会话获取计划
	session := ctx.MustGet("session").(map[string]interface{})
	planData, exists := session[SessionKeyPlan]
	if !exists {
		return nil, fmt.Errorf("no active flow plan found")
	}

	plan, ok := planData.(*FlowPlan)
	if !ok {
		return nil, fmt.Errorf("invalid flow plan in session")
	}

	// 获取Flow
	flow, err := s.GetFlowByUUID(plan.FlowUUID)
	if err != nil {
		return nil, err
	}

	// 创建执行器
	executor := NewFlowExecutorView(flow, plan, ctx, s.logger, s.policyService)

	// 继续执行Flow
	result, err := executor.Execute()
	if err != nil {
		return nil, fmt.Errorf("failed to continue flow: %w", err)
	}

	return result, nil
}

// GetFlowByUUID 根据UUID获取Flow
func (s *FlowService) GetFlowByUUID(flowUUID uuid.UUID) (*Flow, error) {
	var flow Flow
	if err := s.db.Preload("Stages").Where("flow_uuid = ?", flowUUID).First(&flow).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("flow not found: %s", flowUUID.String())
		}
		return nil, fmt.Errorf("failed to get flow: %w", err)
	}

	return &flow, nil
}

// validateFlow 验证Flow配置
func (s *FlowService) validateFlow(flow *Flow) error {
	if flow.Name == "" {
		return fmt.Errorf("flow name cannot be empty")
	}

	if flow.Slug == "" {
		return fmt.Errorf("flow slug cannot be empty")
	}

	if flow.Title == "" {
		return fmt.Errorf("flow title cannot be empty")
	}

	// 验证Designation
	validDesignations := []FlowDesignation{
		FlowDesignationAuthentication,
		FlowDesignationAuthorization,
		FlowDesignationInvalidation,
		FlowDesignationEnrollment,
		FlowDesignationUnenrollment,
		FlowDesignationRecovery,
		FlowDesignationStageSetup,
	}

	valid := false
	for _, designation := range validDesignations {
		if flow.Designation == designation {
			valid = true
			break
		}
	}

	if !valid {
		return fmt.Errorf("invalid flow designation: %s", flow.Designation)
	}

	return nil
}

// CreateDefaultFlows 创建默认Flow
func (s *FlowService) CreateDefaultFlows() error {
	// 创建默认认证Flow
	authFlow := &Flow{
		Name:           "Default Authentication Flow",
		Slug:           "default-authentication-flow",
		Title:          "Welcome to authentik!",
		Designation:    FlowDesignationAuthentication,
		Authentication: FlowAuthNone,
		Layout:         "stacked",
	}

	if err := s.CreateFlow(authFlow); err != nil {
		return fmt.Errorf("failed to create default authentication flow: %w", err)
	}

	// 创建默认授权Flow
	authzFlow := &Flow{
		Name:           "Default Authorization Flow",
		Slug:           "default-authorization-flow",
		Title:          "Authorize Application",
		Designation:    FlowDesignationAuthorization,
		Authentication: FlowAuthRequireAuthenticated,
		Layout:         "stacked",
	}

	if err := s.CreateFlow(authzFlow); err != nil {
		return fmt.Errorf("failed to create default authorization flow: %w", err)
	}

	s.logger.Info("Default flows created successfully")
	return nil
}
