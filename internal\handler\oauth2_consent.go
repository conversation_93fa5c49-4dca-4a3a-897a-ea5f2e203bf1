package handler

import (
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"go-abm-idp/internal/oauth2"
	"go-abm-idp/internal/service"
)

// OAuth2ConsentHandler 处理OAuth2用户同意页面
type OAuth2ConsentHandler struct {
	*Handler
	oauth2Service service.OAuth2AdminService
	oidcService   service.OIDCService
}

// NewOAuth2ConsentHandler 创建OAuth2同意处理器
func NewOAuth2ConsentHandler(
	handler *Handler,
	oauth2Service service.OAuth2AdminService,
	oidcService service.OIDCService,
) *OAuth2ConsentHandler {
	return &OAuth2ConsentHandler{
		Handler:       handler,
		oauth2Service: oauth2Service,
		oidcService:   oidcService,
	}
}

// ShowConsentPage 显示用户同意页面
// @Summary 显示OAuth2用户同意页面
// @Description 显示用户授权同意页面,用户可以选择是否授权应用访问其信
// @Tags OAuth2
// @Accept html
// @Produce html
// @Param client_id query string true "客户端ID"
// @Param redirect_uri query string true "重定向URI"
// @Param scope query string true "权限范围"
// @Param state query string false "状态参

// @Param response_type query string true "响应类型"
// @Success 200 {string} string "同意页面HTML"
// @Router /oauth2/consent [get]
func (h *OAuth2ConsentHandler) ShowConsentPage(ctx *gin.Context) {
	// 从查询参数获取授权请求信
	clientID := ctx.Query("client_id")
	redirectURI := ctx.Query("redirect_uri")
	scope := ctx.Query("scope")
	state := ctx.Query("state")
	responseType := ctx.Query("response_type")
	nonce := ctx.Query("nonce")

	// 验证必需参数
	if err := h.validateConsentRequest(clientID, redirectURI, responseType, scope, state, nonce); err != nil {
		// 使用简化的错误处理
		ctx.JSON(400, gin.H{
			"error":             "invalid_request",
			"error_description": err.Error(),
		})
		return
	}

	// 获取客户端信息
	// 这里应该通过OAuth2AdminService获取客户端详细信息
	clientInfo := map[string]interface{}{
		"client_id":   clientID,
		"name":        "测试应用", // 从数据库获取
		"description": "这是一个测试应用，用于演示OAuth2授权流程",
		"website":     "https://example.com",
		"logo":        "/static/images/default-app-logo.png",
	}

	// 解析权限范围
	scopes := strings.Split(scope, " ")
	scopeDescriptions := h.getScopeDescriptions(scopes)

	// 渲染同意页面
	ctx.HTML(http.StatusOK, "oauth2_consent.html", gin.H{
		"client_info":        clientInfo,
		"scopes":             scopeDescriptions,
		"redirect_uri":       redirectURI,
		"state":              state,
		"response_type":      responseType,
		"nonce":              nonce,
		"authorize_url":      "/oauth2/consent/approve",
		"deny_url":           "/oauth2/consent/deny",
		"original_client_id": clientID,
		"original_scope":     scope,
	})
}

// ApproveConsent 处理用户同意授权
// @Summary 用户同意授权
// @Description 用户同意授权后,生成授权码并重定向到客户
// @Tags OAuth2
// @Accept application/x-www-form-urlencoded
// @Param client_id formData string true "客户端ID"
// @Param redirect_uri formData string true "重定向URI"
// @Param scope formData string true "权限范围"
// @Param state formData string false "状态参

// @Param response_type formData string true "响应类型"
// @Success 302 {string} string "重定向到客户

// @Router /oauth2/consent/approve [post]
func (h *OAuth2ConsentHandler) ApproveConsent(ctx *gin.Context) {
	// 获取表单数据
	clientID := ctx.PostForm("client_id")
	redirectURI := ctx.PostForm("redirect_uri")
	scope := ctx.PostForm("scope")
	state := ctx.PostForm("state")
	responseType := ctx.PostForm("response_type")
	nonce := ctx.PostForm("nonce")

	// 获取当前用户ID（从session或JWT中获取）
	userID := h.getCurrentUserID(ctx)
	if userID == "" {
		h.redirectWithError(ctx, redirectURI, "access_denied", "User not authenticated", state)
		return
	}
	var userIDUint uint
	if userID != "" {
		if id, err := strconv.ParseUint(userID, 10, 32); err == nil {
			userIDUint = uint(id)
		}
	}
	// 处理授权请求
	result, err := h.oauth2Service.HandleAuthorizeRequest(ctx, &oauth2.AuthorizeRequest{
		ClientID:     clientID,
		RedirectURI:  redirectURI,
		ResponseType: responseType,
		Scope:        scope,
		State:        state,
		Nonce:        nonce,
		UserID:       userIDUint,
		Approved:     true, // 用户已同意
	})

	if err != nil {
		h.logger.Error("Failed to handle authorize request", zap.Error(err))
		h.redirectWithError(ctx, redirectURI, "server_error", err.Error(), state)
		return
	}

	// 重定向到客户
	ctx.Redirect(http.StatusFound, result.RedirectURL)
}

// DenyConsent 处理用户拒绝授权
// @Summary 用户拒绝授权
// @Description 用户拒绝授权后,重定向到客户端并返回错误
// @Tags OAuth2
// @Accept application/x-www-form-urlencoded
// @Param redirect_uri formData string true "重定向URI"
// @Param state formData string false "状态参

// @Success 302 {string} string "重定向到客户

// @Router /oauth2/consent/deny [post]
func (h *OAuth2ConsentHandler) DenyConsent(ctx *gin.Context) {
	redirectURI := ctx.PostForm("redirect_uri")
	state := ctx.PostForm("state")

	h.redirectWithError(ctx, redirectURI, "access_denied", "The user denied the request", state)
}

// getScopeDescriptions 获取权限范围的描述
func (h *OAuth2ConsentHandler) getScopeDescriptions(scopes []string) []map[string]string {
	descriptions := make([]map[string]string, 0, len(scopes))

	scopeMap := map[string]map[string]string{
		"openid": {
			"name":        "身份验证",
			"description": "验证您的身份，这是OpenID Connect的基础权限",
			"icon":        "🔐",
			"risk":        "low",
		},
		"profile": {
			"name":        "基本资料",
			"description": "访问您的基本资料信息，包括姓名、用户名、头像等",
			"icon":        "👤",
			"risk":        "low",
		},
		"email": {
			"name":        "邮箱地址",
			"description": "访问您的邮箱地址，用于发送通知和验证身份",
			"icon":        "📧",
			"risk":        "medium",
		},
		"phone": {
			"name":        "手机号码",
			"description": "访问您的手机号码，用于双因素认证和重要通知",
			"icon":        "📱",
			"risk":        "medium",
		},
		"address": {
			"name":        "地址信息",
			"description": "访问您的地址信息，包括国家、地区、邮政编码等",
			"icon":        "📍",
			"risk":        "medium",
		},
		"offline_access": {
			"name":        "离线访问",
			"description": "在您离线时访问您的信息，获得长期访问权限",
			"icon":        "🔄",
			"risk":        "high",
		},
	}

	for _, scope := range scopes {
		if scopeInfo, exists := scopeMap[scope]; exists {
			descriptions = append(descriptions, map[string]string{
				"scope":       scope,
				"name":        scopeInfo["name"],
				"description": scopeInfo["description"],
				"icon":        scopeInfo["icon"],
				"risk":        scopeInfo["risk"],
			})
		} else {
			descriptions = append(descriptions, map[string]string{
				"scope":       scope,
				"name":        scope,
				"description": fmt.Sprintf("访问 %s 相关信息", scope),
				"icon":        "🔧",
				"risk":        "unknown",
			})
		}
	}

	return descriptions
}

// getCurrentUserID 获取当前用户ID
func (h *OAuth2ConsentHandler) getCurrentUserID(ctx *gin.Context) string {
	// 首先检查context中的用户信息
	if userID, exists := ctx.Get("user_id"); exists {
		if uid, ok := userID.(string); ok && uid != "" {
			return uid
		}
	}

	// 然后检查Cookie中的用户信息
	if userID, err := ctx.Cookie("user_id"); err == nil && userID != "" {
		// 将Cookie中的用户信息设置到context
		ctx.Set("user_id", userID)
		return userID
	}

	// 没有找到用户信息,用户未登录
	return ""
}

// redirectWithError 重定向并返回错误
func (h *OAuth2ConsentHandler) redirectWithError(ctx *gin.Context, redirectURI, errorCode, errorDescription, state string) {
	if redirectURI == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":             errorCode,
			"error_description": errorDescription,
		})
		return
	}

	// 构建错误重定向URL
	u, err := url.Parse(redirectURI)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":             "invalid_redirect_uri",
			"error_description": "Invalid redirect URI",
		})
		return
	}

	query := u.Query()
	query.Set("error", errorCode)
	query.Set("error_description", errorDescription)
	if state != "" {
		query.Set("state", state)
	}
	u.RawQuery = query.Encode()

	ctx.Redirect(http.StatusFound, u.String())
}

// validateConsentRequest 验证同意页面请求参数
func (h *OAuth2ConsentHandler) validateConsentRequest(
	clientID, redirectURI, responseType, scope, state, nonce string,
) error {
	// 1. 验证client_id
	if clientID == "" {
		return fmt.Errorf("client_id is required")
	}

	// 2. 验证response_type
	supportedResponseTypes := []string{"code", "token", "id_token"}
	validResponseType := false
	for _, rt := range supportedResponseTypes {
		if responseType == rt {
			validResponseType = true
			break
		}
	}
	if !validResponseType {
		return fmt.Errorf("unsupported response_type: %s", responseType)
	}

	// 3. 验证redirect_uri
	if redirectURI == "" {
		return fmt.Errorf("redirect_uri is required")
	}

	// 4. 验证scope
	if scope == "" {
		return fmt.Errorf("scope is required")
	}

	// 5. 验证state参数 (可选)
	// state参数是可选的，不需要验证

	// 6. 验证nonce参数 (OpenID Connect需要)
	if strings.Contains(scope, "openid") && nonce == "" {
		return fmt.Errorf("nonce is required for OpenID Connect requests")
	}

	return nil
}
