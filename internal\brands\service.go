package brands

import (
	"fmt"
	"strings"
	"time"

	"go-abm-idp/internal/flows"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// BrandService 品牌服务
type BrandService struct {
	db          *gorm.DB
	logger      *zap.Logger
	flowService *flows.FlowService
}

// NewBrandService 创建新的品牌服务
func NewBrandService(db *gorm.DB, logger *zap.Logger, flowService *flows.FlowService) *BrandService {
	return &BrandService{
		db:          db,
		logger:      logger,
		flowService: flowService,
	}
}

// CreateBrand 创建品牌
func (s *BrandService) CreateBrand(brand *Brand) error {
	if err := brand.Validate(); err != nil {
		return fmt.Errorf("brand validation failed: %w", err)
	}
	
	brand.BrandUUID = uuid.New()
	brand.CreatedAt = time.Now()
	brand.UpdatedAt = time.Now()
	
	// 如果设置为默认品牌，需要取消其他品牌的默认状态
	if brand.Default {
		if err := s.clearDefaultBrand(); err != nil {
			return fmt.Errorf("failed to clear default brand: %w", err)
		}
	}
	
	if err := s.db.Create(brand).Error; err != nil {
		return fmt.Errorf("failed to create brand: %w", err)
	}
	
	s.logger.Info("Brand created",
		zap.String("brand_uuid", brand.BrandUUID.String()),
		zap.String("domain", brand.Domain),
		zap.Bool("default", brand.Default))
	
	return nil
}

// GetBrandByDomain 根据域名获取品牌
func (s *BrandService) GetBrandByDomain(domain string) (*Brand, error) {
	var brand Brand
	
	// 首先尝试精确匹配
	err := s.db.Where("domain = ?", domain).First(&brand).Error
	if err == nil {
		return &brand, nil
	}
	
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to get brand by domain: %w", err)
	}
	
	// 尝试通配符匹配
	err = s.db.Where("domain LIKE ?", "*."+extractRootDomain(domain)).First(&brand).Error
	if err == nil {
		return &brand, nil
	}
	
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to get brand by wildcard domain: %w", err)
	}
	
	// 返回默认品牌
	return s.GetDefaultBrand()
}

// GetDefaultBrand 获取默认品牌
func (s *BrandService) GetDefaultBrand() (*Brand, error) {
	var brand Brand
	if err := s.db.Where("default = ?", true).First(&brand).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("no default brand found")
		}
		return nil, fmt.Errorf("failed to get default brand: %w", err)
	}
	
	return &brand, nil
}

// GetBrandByUUID 根据UUID获取品牌
func (s *BrandService) GetBrandByUUID(brandUUID uuid.UUID) (*Brand, error) {
	var brand Brand
	if err := s.db.Where("brand_uuid = ?", brandUUID).First(&brand).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("brand not found: %s", brandUUID.String())
		}
		return nil, fmt.Errorf("failed to get brand: %w", err)
	}
	
	return &brand, nil
}

// ListBrands 列出所有品牌
func (s *BrandService) ListBrands() ([]Brand, error) {
	var brands []Brand
	if err := s.db.Find(&brands).Error; err != nil {
		return nil, fmt.Errorf("failed to list brands: %w", err)
	}
	
	return brands, nil
}

// UpdateBrand 更新品牌
func (s *BrandService) UpdateBrand(brand *Brand) error {
	if err := brand.Validate(); err != nil {
		return fmt.Errorf("brand validation failed: %w", err)
	}
	
	// 如果设置为默认品牌，需要取消其他品牌的默认状态
	if brand.Default {
		if err := s.clearDefaultBrand(); err != nil {
			return fmt.Errorf("failed to clear default brand: %w", err)
		}
	}
	
	brand.UpdatedAt = time.Now()
	
	if err := s.db.Save(brand).Error; err != nil {
		return fmt.Errorf("failed to update brand: %w", err)
	}
	
	s.logger.Info("Brand updated",
		zap.String("brand_uuid", brand.BrandUUID.String()),
		zap.String("domain", brand.Domain))
	
	return nil
}

// DeleteBrand 删除品牌
func (s *BrandService) DeleteBrand(brandUUID uuid.UUID) error {
	// 检查是否为默认品牌
	brand, err := s.GetBrandByUUID(brandUUID)
	if err != nil {
		return err
	}
	
	if brand.Default {
		return fmt.Errorf("cannot delete default brand")
	}
	
	// 删除相关的设置、主题、资源等
	if err := s.db.Delete(&BrandSettings{}, "brand_uuid = ?", brandUUID).Error; err != nil {
		return fmt.Errorf("failed to delete brand settings: %w", err)
	}
	
	if err := s.db.Delete(&BrandTheme{}, "brand_uuid = ?", brandUUID).Error; err != nil {
		return fmt.Errorf("failed to delete brand themes: %w", err)
	}
	
	if err := s.db.Delete(&BrandAsset{}, "brand_uuid = ?", brandUUID).Error; err != nil {
		return fmt.Errorf("failed to delete brand assets: %w", err)
	}
	
	if err := s.db.Delete(&BrandLocalization{}, "brand_uuid = ?", brandUUID).Error; err != nil {
		return fmt.Errorf("failed to delete brand localizations: %w", err)
	}
	
	// 删除品牌本身
	if err := s.db.Delete(&Brand{}, "brand_uuid = ?", brandUUID).Error; err != nil {
		return fmt.Errorf("failed to delete brand: %w", err)
	}
	
	s.logger.Info("Brand deleted", zap.String("brand_uuid", brandUUID.String()))
	return nil
}

// GetBrandContext 获取品牌上下文
func (s *BrandService) GetBrandContext(ctx *gin.Context) (*BrandContext, error) {
	// 从请求获取域名
	domain := ctx.Request.Host
	
	// 获取品牌
	brand, err := s.GetBrandByDomain(domain)
	if err != nil {
		return nil, fmt.Errorf("failed to get brand for domain %s: %w", domain, err)
	}
	
	// 创建品牌上下文
	brandContext := &BrandContext{
		Brand:         brand,
		RequestDomain: domain,
		UserAgent:     ctx.GetHeader("User-Agent"),
		ClientIP:      ctx.ClientIP(),
	}
	
	// 设置用户信息
	if user, exists := ctx.Get("user"); exists {
		brandContext.User = user
	}
	
	// 设置Flow信息
	if flow, exists := ctx.Get("flow"); exists {
		brandContext.CurrentFlow = flow
	}
	
	// 设置应用信息
	if app, exists := ctx.Get("application"); exists {
		brandContext.Application = app
	}
	
	return brandContext, nil
}

// GetFlowForBrand 获取品牌的特定Flow
func (s *BrandService) GetFlowForBrand(brand *Brand, flowType string) (*flows.Flow, error) {
	var flowUUID *uuid.UUID
	
	switch flowType {
	case "authentication":
		flowUUID = brand.FlowAuthenticationUUID
	case "invalidation":
		flowUUID = brand.FlowInvalidationUUID
	case "recovery":
		flowUUID = brand.FlowRecoveryUUID
	case "unenrollment":
		flowUUID = brand.FlowUnenrollmentUUID
	case "user_settings":
		flowUUID = brand.FlowUserSettingsUUID
	case "device_code":
		flowUUID = brand.FlowDeviceCodeUUID
	default:
		return nil, fmt.Errorf("unknown flow type: %s", flowType)
	}
	
	// 如果品牌没有配置特定Flow，使用默认Flow
	if flowUUID == nil {
		designation := flows.FlowDesignation(flowType)
		return s.flowService.GetFlowByDesignation(designation)
	}
	
	return s.flowService.GetFlowByUUID(*flowUUID)
}

// CreateBrandTheme 创建品牌主题
func (s *BrandService) CreateBrandTheme(theme *BrandTheme) error {
	theme.ThemeUUID = uuid.New()
	theme.CreatedAt = time.Now()
	theme.UpdatedAt = time.Now()
	
	if err := s.db.Create(theme).Error; err != nil {
		return fmt.Errorf("failed to create brand theme: %w", err)
	}
	
	s.logger.Info("Brand theme created",
		zap.String("theme_uuid", theme.ThemeUUID.String()),
		zap.String("brand_uuid", theme.BrandUUID.String()),
		zap.String("name", theme.Name))
	
	return nil
}

// GetBrandTheme 获取品牌主题
func (s *BrandService) GetBrandTheme(brandUUID uuid.UUID) (*BrandTheme, error) {
	var theme BrandTheme
	err := s.db.Where("brand_uuid = ? AND enabled = ?", brandUUID, true).First(&theme).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 返回默认主题
			return s.createDefaultTheme(brandUUID), nil
		}
		return nil, fmt.Errorf("failed to get brand theme: %w", err)
	}
	
	return &theme, nil
}

// CreateBrandAsset 创建品牌资源
func (s *BrandService) CreateBrandAsset(asset *BrandAsset) error {
	asset.AssetUUID = uuid.New()
	asset.CreatedAt = time.Now()
	asset.UpdatedAt = time.Now()
	
	if err := s.db.Create(asset).Error; err != nil {
		return fmt.Errorf("failed to create brand asset: %w", err)
	}
	
	s.logger.Info("Brand asset created",
		zap.String("asset_uuid", asset.AssetUUID.String()),
		zap.String("brand_uuid", asset.BrandUUID.String()),
		zap.String("name", asset.Name),
		zap.String("type", asset.Type))
	
	return nil
}

// clearDefaultBrand 清除默认品牌标记
func (s *BrandService) clearDefaultBrand() error {
	return s.db.Model(&Brand{}).Where("default = ?", true).Update("default", false).Error
}

// extractRootDomain 提取根域名
func extractRootDomain(domain string) string {
	parts := strings.Split(domain, ".")
	if len(parts) >= 2 {
		return strings.Join(parts[len(parts)-2:], ".")
	}
	return domain
}

// createDefaultTheme 创建默认主题
func (s *BrandService) createDefaultTheme(brandUUID uuid.UUID) *BrandTheme {
	return &BrandTheme{
		ThemeUUID:       uuid.New(),
		BrandUUID:       brandUUID,
		Name:            "Default Theme",
		Description:     "Default authentik theme",
		PrimaryColor:    "#1976d2",
		SecondaryColor:  "#dc004e",
		BackgroundColor: "#fafafa",
		SurfaceColor:    "#ffffff",
		ErrorColor:      "#f44336",
		WarningColor:    "#ff9800",
		InfoColor:       "#2196f3",
		SuccessColor:    "#4caf50",
		FontFamily:      "Roboto, sans-serif",
		FontSize:        "14px",
		BorderRadius:    "4px",
		Spacing:         "8px",
		Enabled:         true,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
}

// CreateDefaultBrand 创建默认品牌
func (s *BrandService) CreateDefaultBrand() error {
	// 检查是否已存在默认品牌
	_, err := s.GetDefaultBrand()
	if err == nil {
		return nil // 默认品牌已存在
	}
	
	// 创建默认品牌
	defaultBrand := &Brand{
		Domain:            "localhost",
		Default:           true,
		BrandingTitle:     "authentik",
		BrandingLogo:      "/static/dist/assets/icons/icon_left_brand.svg",
		BrandingFavicon:   "/static/dist/assets/icons/icon.png",
		AttributesUsername: "username",
		AttributesEmail:   "email",
		AttributesName:    "name",
		AttributesGroup:   "groups",
	}
	
	if err := s.CreateBrand(defaultBrand); err != nil {
		return fmt.Errorf("failed to create default brand: %w", err)
	}
	
	s.logger.Info("Default brand created successfully")
	return nil
}
