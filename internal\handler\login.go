package handler

import (
	"net/http"
	"net/url"

	"github.com/gin-gonic/gin"
)

// LoginHandler 处理用户登录
type LoginHandler struct {
	*Handler
}

// NewLoginHandler 创建登录处理器
func NewLoginHandler(handler *Handler) *LoginHandler {
	return &LoginHandler{
		Handler: handler,
	}
}

// ShowLoginPage 显示登录页面
// @Summary 显示用户登录页面
// @Description 显示用户登录页面,用于OAuth2授权流程中的用户认证,支持login_hint参数
// @Tags 认证
// @Accept html
// @Produce html
// @Param redirect_uri query string false "登录成功后的重定向URI"
// @Param login_hint query string false "登录提示（邮箱、用户名等）"
// @Param login_hint_type query string false "登录提示类型"
// @Success 200 {string} string "登录页面HTML"
// @Router /login [get]
func (h *LoginHandler) ShowLoginPage(ctx *gin.Context) {
	redirectURI := ctx.Query("redirect_uri")

	// 获取login_hint参数
	loginHint := ctx.Query("login_hint")
	loginHintType := ctx.Query("login_hint_type")

	// 构建页面数据
	pageData := gin.H{
		"title":           "用户登录",
		"redirect_uri":    redirectURI,
		"login_hint":      loginHint,
		"login_hint_type": loginHintType,
		"show_hint":       loginHint != "",
	}

	ctx.HTML(http.StatusOK, "login.html", pageData)
}

// ProcessLogin 处理登录请求
// @Summary 处理用户登录
// @Description 验证用户凭证并建立会话
// @Tags 认证
// @Accept application/x-www-form-urlencoded
// @Param username formData string true "用户名"
// @Param password formData string true "密码"
// @Param redirect_uri formData string false "登录成功后的重定向URI"
// @Success 302 {string} string "重定向到目标页面"
// @Failure 400 {object} map[string]interface{} "登录失败"
// @Router /login [post]
func (h *LoginHandler) ProcessLogin(ctx *gin.Context) {
	username := ctx.PostForm("username")
	password := ctx.PostForm("password")
	redirectURI := ctx.PostForm("redirect_uri")

	// 基本参数验证
	if username == "" {
		ctx.HTML(http.StatusBadRequest, "login.html", gin.H{
			"error":        "用户名不能为空",
			"redirect_uri": redirectURI,
		})
		return
	}

	// 直接通过登录,不验证密码
	userID := h.authenticateUser(username, password)

	// 建立用户会话
	h.createUserSession(ctx, userID, username)

	// 重定向到目标页面
	if redirectURI != "" {
		// 验证重定向URI的安全性
		if h.isValidRedirectURI(redirectURI) {
			ctx.Redirect(http.StatusFound, redirectURI)
			return
		}
	}

	// 默认重定向到仪表板
	ctx.Redirect(http.StatusFound, "/dashboard")
}

// Logout 处理用户登出
// @Summary 用户登出
// @Description 清除用户会话并重定向到登录页
// @Tags 认证
// @Success 302 {string} string "重定向到登录页面"
// @Router /logout [post]
func (h *LoginHandler) Logout(ctx *gin.Context) {
	// 清除用户会话
	h.clearUserSession(ctx)

	// 重定向到登录页面
	ctx.Redirect(http.StatusFound, "/login")
}

// authenticateUser 验证用户凭证
func (h *LoginHandler) authenticateUser(username, password string) string {
	// 演示环境:只要有用户名就直接通过
	// 不验证密码,密码可以为空或任意
	if username != "" {
		// 根据用户名返回对应的用户ID
		if username == "admin" {
			return "1"
		} else if username == "demo" {
			return "2"
		} else {
			// 为其他用户名创建临时用户ID
			return "999"
		}
	}

	return ""
}

// createUserSession 创建用户会话
func (h *LoginHandler) createUserSession(ctx *gin.Context, userID, username string) {
	// 在实际实现中,这里应该:
	// 1. 生成安全的会话令
	// 2. 将会话信息存储到Redis或数据库
	// 3. 设置安全的HTTP Cookie

	// 简化实现:使用Gin的session或context
	ctx.Set("user_id", userID)
	ctx.Set("username", username)
	ctx.Set("authenticated", true)

	// 设置Cookie（简化实现）
	ctx.SetCookie("user_id", userID, 3600, "/", "", false, true)
	ctx.SetCookie("username", username, 3600, "/", "", false, false)
}

// clearUserSession 清除用户会话
func (h *LoginHandler) clearUserSession(ctx *gin.Context) {
	// 清除context中的用户信息
	ctx.Set("user_id", nil)
	ctx.Set("username", nil)
	ctx.Set("authenticated", false)

	// 清除Cookie
	ctx.SetCookie("user_id", "", -1, "/", "", false, true)
	ctx.SetCookie("username", "", -1, "/", "", false, false)
}

// isValidRedirectURI 验证重定向URI的安全
func (h *LoginHandler) isValidRedirectURI(redirectURI string) bool {
	// 解析URI
	u, err := url.Parse(redirectURI)
	if err != nil {
		return false
	}

	// 只允许相对路径或同域名的绝对路径
	if u.IsAbs() {
		// 如果是绝对路径,检查是否为同域
		if u.Host != "" {
			// 在实际实现中,这里应该检查是否为允许的域名列
			// 简化实现:只允许localhost127.0.0.1
			if u.Host != "localhost:8000" && u.Host != "127.0.0.1:8000" {
				return false
			}
		}
	}

	return true
}
