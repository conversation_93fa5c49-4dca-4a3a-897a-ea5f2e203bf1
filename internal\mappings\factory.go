package mappings

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PropertyMappingFactory 属性映射工厂
type PropertyMappingFactory struct {
	db *gorm.DB
}

// NewPropertyMappingFactory 创建新的属性映射工厂
func NewPropertyMappingFactory(db *gorm.DB) *PropertyMappingFactory {
	return &PropertyMappingFactory{db: db}
}

// CreateMapping 根据类型创建属性映射
func (f *PropertyMappingFactory) CreateMapping(mappingType PropertyMappingType, name string) (PropertyMapping, error) {
	switch mappingType {
	case PropertyMappingTypeScope:
		return f.createScopeMapping(name)
	case PropertyMappingTypeSAML:
		return f.createSAMLMapping(name)
	case PropertyMappingTypeLDAP:
		return f.createLDAPMapping(name)
	case PropertyMappingTypeNotification:
		return f.createNotificationMapping(name)
	default:
		return nil, fmt.Errorf("unknown mapping type: %s", mappingType)
	}
}

// LoadMapping 从数据库加载属性映射
func (f *PropertyMappingFactory) LoadMapping(mappingUUID uuid.UUID) (PropertyMapping, error) {
	// 首先获取基础映射信息
	var baseMapping struct {
		MappingUUID uuid.UUID `gorm:"column:mapping_uuid"`
		MappingType string    `gorm:"column:mapping_type"`
		Name        string    `gorm:"column:name"`
	}
	
	if err := f.db.Table("property_mappings").Where("mapping_uuid = ?", mappingUUID).First(&baseMapping).Error; err != nil {
		return nil, fmt.Errorf("failed to load base mapping: %w", err)
	}
	
	// 根据类型加载具体的映射
	switch PropertyMappingType(baseMapping.MappingType) {
	case PropertyMappingTypeScope:
		return f.loadScopeMapping(mappingUUID)
	case PropertyMappingTypeSAML:
		return f.loadSAMLMapping(mappingUUID)
	case PropertyMappingTypeLDAP:
		return f.loadLDAPMapping(mappingUUID)
	case PropertyMappingTypeNotification:
		return f.loadNotificationMapping(mappingUUID)
	default:
		return nil, fmt.Errorf("unknown mapping type: %s", baseMapping.MappingType)
	}
}

// SaveMapping 保存属性映射到数据库
func (f *PropertyMappingFactory) SaveMapping(mapping PropertyMapping) error {
	// 确定映射类型
	var mappingType PropertyMappingType
	switch mapping.(type) {
	case *ScopeMapping:
		mappingType = PropertyMappingTypeScope
	case *SAMLPropertyMapping:
		mappingType = PropertyMappingTypeSAML
	case *LDAPPropertyMapping:
		mappingType = PropertyMappingTypeLDAP
	case *NotificationPropertyMapping:
		mappingType = PropertyMappingTypeNotification
	default:
		return fmt.Errorf("unknown mapping type: %T", mapping)
	}
	
	return f.saveMapping(mapping, mappingType)
}

// UpdateMapping 更新属性映射
func (f *PropertyMappingFactory) UpdateMapping(mapping PropertyMapping) error {
	// 确定映射类型
	var mappingType PropertyMappingType
	switch mapping.(type) {
	case *ScopeMapping:
		mappingType = PropertyMappingTypeScope
	case *SAMLPropertyMapping:
		mappingType = PropertyMappingTypeSAML
	case *LDAPPropertyMapping:
		mappingType = PropertyMappingTypeLDAP
	case *NotificationPropertyMapping:
		mappingType = PropertyMappingTypeNotification
	default:
		return fmt.Errorf("unknown mapping type: %T", mapping)
	}
	
	return f.updateMapping(mapping, mappingType)
}

// createScopeMapping 创建Scope映射
func (f *PropertyMappingFactory) createScopeMapping(name string) (*ScopeMapping, error) {
	mapping := &ScopeMapping{
		BasePropertyMapping: BasePropertyMapping{
			MappingUUID: uuid.New(),
			Name:        name,
			Expression:  "return user",
		},
		ScopeName: "openid",
	}
	
	return mapping, nil
}

// createSAMLMapping 创建SAML映射
func (f *PropertyMappingFactory) createSAMLMapping(name string) (*SAMLPropertyMapping, error) {
	mapping := &SAMLPropertyMapping{
		BasePropertyMapping: BasePropertyMapping{
			MappingUUID: uuid.New(),
			Name:        name,
			Expression:  "return user.username",
		},
		SAMLName:   "username",
		NameFormat: "urn:oasis:names:tc:SAML:2.0:attrname-format:basic",
	}
	
	return mapping, nil
}

// createLDAPMapping 创建LDAP映射
func (f *PropertyMappingFactory) createLDAPMapping(name string) (*LDAPPropertyMapping, error) {
	mapping := &LDAPPropertyMapping{
		BasePropertyMapping: BasePropertyMapping{
			MappingUUID: uuid.New(),
			Name:        name,
			Expression:  "return ldap.cn",
		},
		ObjectField: "username",
	}
	
	return mapping, nil
}

// createNotificationMapping 创建通知映射
func (f *PropertyMappingFactory) createNotificationMapping(name string) (*NotificationPropertyMapping, error) {
	mapping := &NotificationPropertyMapping{
		BasePropertyMapping: BasePropertyMapping{
			MappingUUID: uuid.New(),
			Name:        name,
			Expression:  "return 'Hello World'",
		},
	}
	
	return mapping, nil
}

// saveMapping 保存映射到数据库
func (f *PropertyMappingFactory) saveMapping(mapping PropertyMapping, mappingType PropertyMappingType) error {
	// 开始事务
	tx := f.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	
	// 保存基础映射信息
	baseMapping := map[string]interface{}{
		"mapping_uuid": mapping.GetUUID(),
		"mapping_type": string(mappingType),
		"name":         mapping.GetName(),
		"created_at":   time.Now(),
		"updated_at":   time.Now(),
	}
	
	if err := tx.Table("property_mappings").Create(baseMapping).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to save base mapping: %w", err)
	}
	
	// 根据类型保存具体映射信息
	switch mappingType {
	case PropertyMappingTypeScope:
		if err := tx.Table("scope_mappings").Create(mapping).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to save scope mapping: %w", err)
		}
	case PropertyMappingTypeSAML:
		if err := tx.Table("saml_property_mappings").Create(mapping).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to save SAML mapping: %w", err)
		}
	case PropertyMappingTypeLDAP:
		if err := tx.Table("ldap_property_mappings").Create(mapping).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to save LDAP mapping: %w", err)
		}
	case PropertyMappingTypeNotification:
		if err := tx.Table("notification_property_mappings").Create(mapping).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to save notification mapping: %w", err)
		}
	}
	
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit mapping creation: %w", err)
	}
	
	return nil
}

// updateMapping 更新映射到数据库
func (f *PropertyMappingFactory) updateMapping(mapping PropertyMapping, mappingType PropertyMappingType) error {
	// 开始事务
	tx := f.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	
	// 更新基础映射信息
	baseMapping := map[string]interface{}{
		"name":       mapping.GetName(),
		"updated_at": time.Now(),
	}
	
	if err := tx.Table("property_mappings").Where("mapping_uuid = ?", mapping.GetUUID()).Updates(baseMapping).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update base mapping: %w", err)
	}
	
	// 根据类型更新具体映射信息
	switch mappingType {
	case PropertyMappingTypeScope:
		if err := tx.Table("scope_mappings").Where("mapping_uuid = ?", mapping.GetUUID()).Updates(mapping).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to update scope mapping: %w", err)
		}
	case PropertyMappingTypeSAML:
		if err := tx.Table("saml_property_mappings").Where("mapping_uuid = ?", mapping.GetUUID()).Updates(mapping).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to update SAML mapping: %w", err)
		}
	case PropertyMappingTypeLDAP:
		if err := tx.Table("ldap_property_mappings").Where("mapping_uuid = ?", mapping.GetUUID()).Updates(mapping).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to update LDAP mapping: %w", err)
		}
	case PropertyMappingTypeNotification:
		if err := tx.Table("notification_property_mappings").Where("mapping_uuid = ?", mapping.GetUUID()).Updates(mapping).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to update notification mapping: %w", err)
		}
	}
	
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit mapping update: %w", err)
	}
	
	return nil
}

// loadScopeMapping 加载Scope映射
func (f *PropertyMappingFactory) loadScopeMapping(mappingUUID uuid.UUID) (*ScopeMapping, error) {
	var mapping ScopeMapping
	
	// 加载基础信息
	if err := f.db.Table("property_mappings").Where("mapping_uuid = ?", mappingUUID).First(&mapping.BasePropertyMapping).Error; err != nil {
		return nil, fmt.Errorf("failed to load base mapping: %w", err)
	}
	
	// 加载具体信息
	if err := f.db.Table("scope_mappings").Where("mapping_uuid = ?", mappingUUID).First(&mapping).Error; err != nil {
		return nil, fmt.Errorf("failed to load scope mapping: %w", err)
	}
	
	return &mapping, nil
}

// loadSAMLMapping 加载SAML映射
func (f *PropertyMappingFactory) loadSAMLMapping(mappingUUID uuid.UUID) (*SAMLPropertyMapping, error) {
	var mapping SAMLPropertyMapping
	
	// 加载基础信息
	if err := f.db.Table("property_mappings").Where("mapping_uuid = ?", mappingUUID).First(&mapping.BasePropertyMapping).Error; err != nil {
		return nil, fmt.Errorf("failed to load base mapping: %w", err)
	}
	
	// 加载具体信息
	if err := f.db.Table("saml_property_mappings").Where("mapping_uuid = ?", mappingUUID).First(&mapping).Error; err != nil {
		return nil, fmt.Errorf("failed to load SAML mapping: %w", err)
	}
	
	return &mapping, nil
}

// loadLDAPMapping 加载LDAP映射
func (f *PropertyMappingFactory) loadLDAPMapping(mappingUUID uuid.UUID) (*LDAPPropertyMapping, error) {
	var mapping LDAPPropertyMapping
	
	// 加载基础信息
	if err := f.db.Table("property_mappings").Where("mapping_uuid = ?", mappingUUID).First(&mapping.BasePropertyMapping).Error; err != nil {
		return nil, fmt.Errorf("failed to load base mapping: %w", err)
	}
	
	// 加载具体信息
	if err := f.db.Table("ldap_property_mappings").Where("mapping_uuid = ?", mappingUUID).First(&mapping).Error; err != nil {
		return nil, fmt.Errorf("failed to load LDAP mapping: %w", err)
	}
	
	return &mapping, nil
}

// loadNotificationMapping 加载通知映射
func (f *PropertyMappingFactory) loadNotificationMapping(mappingUUID uuid.UUID) (*NotificationPropertyMapping, error) {
	var mapping NotificationPropertyMapping
	
	// 加载基础信息
	if err := f.db.Table("property_mappings").Where("mapping_uuid = ?", mappingUUID).First(&mapping.BasePropertyMapping).Error; err != nil {
		return nil, fmt.Errorf("failed to load base mapping: %w", err)
	}
	
	// 加载具体信息
	if err := f.db.Table("notification_property_mappings").Where("mapping_uuid = ?", mappingUUID).First(&mapping).Error; err != nil {
		return nil, fmt.Errorf("failed to load notification mapping: %w", err)
	}
	
	return &mapping, nil
}
