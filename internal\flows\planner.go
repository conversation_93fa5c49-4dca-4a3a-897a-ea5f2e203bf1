package flows

import (
	"context"
	"fmt"
	"go-abm-idp/internal/events"
	"go-abm-idp/internal/policies"
	"sort"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// FlowPlanner Flow规划器，负责创建Flow执行计划
type FlowPlanner struct {
	flow          *Flow
	logger        *zap.Logger
	policyService *policies.PolicyService
	eventService  *events.EventService

	// 配置选项
	UseCache        bool
	AllowEmptyFlows bool
}

// NewFlowPlanner 创建新的Flow规划器
func NewFlowPlanner(flow *Flow, logger *zap.Logger, policyService *policies.PolicyService, eventService *events.EventService) *FlowPlanner {
	return &FlowPlanner{
		flow:            flow,
		logger:          logger,
		policyService:   policyService,
		eventService:    eventService,
		UseCache:        true,
		AllowEmptyFlows: false,
	}
}

// Plan 创建Flow执行计划
func (p *FlowPlanner) Plan(ctx *gin.Context, defaultContext map[string]interface{}) (*FlowPlan, error) {
	p.logger.Info("Starting flow planning process",
		zap.String("flow_slug", p.flow.Slug),
		zap.String("flow_name", p.flow.Name))

	// 创建基础计划
	plan := &FlowPlan{
		FlowUUID:          p.flow.FlowUUID,
		Context:           make(map[string]interface{}),
		CurrentStageIndex: 0,
		CreatedAt:         time.Now(),
	}

	// 设置默认上下文
	if defaultContext != nil {
		for key, value := range defaultContext {
			plan.Context[key] = value
		}
	}

	// 获取并排序Flow的Stages
	bindings, err := p.getFlowStageBindings()
	if err != nil {
		return nil, fmt.Errorf("failed to get flow stage bindings: %w", err)
	}

	// 按顺序排序
	sort.Slice(bindings, func(i, j int) bool {
		return bindings[i].Order < bindings[j].Order
	})

	// 评估策略并过滤Stages
	filteredBindings := []FlowStageBinding{}
	for _, binding := range bindings {
		if binding.EvaluateOnPlan {
			// 在规划阶段评估策略
			if p.evaluateStagePolicy(ctx, &binding, plan) {
				filteredBindings = append(filteredBindings, binding)
			} else {
				p.logger.Debug("Stage filtered out by policy during planning",
					zap.String("stage_name", binding.Stage.GetName()),
					zap.String("stage_uuid", binding.StageUUID.String()))
			}
		} else {
			// 运行时评估策略
			filteredBindings = append(filteredBindings, binding)
		}
	}

	plan.Bindings = filteredBindings

	// 检查是否允许空Flow
	if len(plan.Bindings) == 0 && !p.AllowEmptyFlows {
		return nil, fmt.Errorf("flow %s has no applicable stages", p.flow.Slug)
	}

	p.logger.Info("Flow planning completed",
		zap.String("flow_slug", p.flow.Slug),
		zap.Int("total_stages", len(plan.Bindings)))

	return plan, nil
}

// getFlowStageBindings 获取Flow的Stage绑定
func (p *FlowPlanner) getFlowStageBindings() ([]FlowStageBinding, error) {
	// 这里应该从数据库获取，暂时返回Flow中的Stages
	return p.flow.Stages, nil
}

// evaluateStagePolicy 评估Stage策略
func (p *FlowPlanner) evaluateStagePolicy(ctx *gin.Context, binding *FlowStageBinding, plan *FlowPlan) bool {
	// 首先检查基本认证要求
	if !p.checkAuthenticationRequirement(plan) {
		return false
	}

	// 如果没有策略服务，使用简化逻辑
	if p.policyService == nil {
		return true
	}

	// 创建策略请求
	request := &policies.PolicyRequest{
		HTTPRequest: ctx,
		Context:     plan.Context,
	}

	// 设置用户信息
	if user, exists := plan.GetContext(PlanContextPendingUser); exists {
		request.User = user
	}

	// 评估FlowStageBinding的策略
	mode := policies.PolicyEngineMode(binding.PolicyEngineMode)
	result, err := p.policyService.EvaluatePolicies(
		context.Background(),
		binding.BindingUUID,
		"flow_stage_binding",
		mode,
		request,
	)

	if err != nil {
		p.logger.Error("Failed to evaluate stage policies",
			zap.String("stage_name", binding.Stage.GetName()),
			zap.Error(err))
		return false
	}

	p.logger.Debug("Stage policy evaluation completed",
		zap.String("stage_name", binding.Stage.GetName()),
		zap.Bool("passing", result.Passing),
		zap.Strings("messages", result.Messages))

	return result.Passing
}

// checkAuthenticationRequirement 检查认证要求
func (p *FlowPlanner) checkAuthenticationRequirement(plan *FlowPlan) bool {
	switch p.flow.Authentication {
	case FlowAuthRequireAuthenticated:
		// 检查用户是否已认证
		if user, exists := plan.GetContext(PlanContextPendingUser); !exists || user == nil {
			p.logger.Debug("Flow requires authentication but no user in context")
			return false
		}
	case FlowAuthRequireUnauthenticated:
		// 检查用户是否未认证
		if user, exists := plan.GetContext(PlanContextPendingUser); exists && user != nil {
			p.logger.Debug("Flow requires unauthenticated user but user exists in context")
			return false
		}
	}

	return true
}

// FlowNonApplicableException Flow不适用异常
type FlowNonApplicableException struct {
	Messages []string
}

func (e *FlowNonApplicableException) Error() string {
	return fmt.Sprintf("Flow not applicable: %v", e.Messages)
}

// FlowExecutorView Flow执行器视图
type FlowExecutorView struct {
	executor      *FlowExecutor
	logger        *zap.Logger
	policyService *policies.PolicyService
}

// NewFlowExecutorView 创建新的Flow执行器视图
func NewFlowExecutorView(flow *Flow, plan *FlowPlan, ctx *gin.Context, logger *zap.Logger, policyService *policies.PolicyService) *FlowExecutorView {
	executor := &FlowExecutor{
		Flow:      flow,
		Plan:      plan,
		Request:   ctx,
		History:   []StageExecutionRecord{},
		StartedAt: time.Now(),
	}

	return &FlowExecutorView{
		executor:      executor,
		logger:        logger,
		policyService: policyService,
	}
}

// Execute 执行Flow
func (v *FlowExecutorView) Execute() (*StageResult, error) {
	v.logger.Info("Starting flow execution",
		zap.String("flow_slug", v.executor.Flow.Slug),
		zap.Int("total_stages", len(v.executor.Plan.Bindings)))

	for v.executor.Plan.HasStages() {
		binding := v.executor.Plan.NextStage()
		if binding == nil {
			break
		}

		// 运行时策略评估
		if binding.ReEvaluatePolicies && !v.evaluateStagePolicy(binding) {
			v.logger.Debug("Stage filtered out by policy during execution",
				zap.String("stage_name", binding.Stage.GetName()))
			v.executor.Plan.AdvanceStage()
			continue
		}

		// 执行Stage
		result, err := v.executeStage(binding)
		if err != nil {
			return nil, fmt.Errorf("failed to execute stage %s: %w", binding.Stage.GetName(), err)
		}

		// 记录执行历史
		v.recordStageExecution(binding, result, nil)

		// 处理Stage结果
		if !result.Success {
			return result, nil
		}

		// 更新计划上下文
		if result.Context != nil {
			for key, value := range result.Context {
				v.executor.Plan.SetContext(key, value)
			}
		}

		// 如果需要用户交互，返回结果
		if result.RequiresInteraction {
			return result, nil
		}

		// 推进到下一个Stage
		v.executor.Plan.AdvanceStage()
	}

	v.logger.Info("Flow execution completed",
		zap.String("flow_slug", v.executor.Flow.Slug),
		zap.Int("executed_stages", len(v.executor.History)))

	// Flow执行完成
	return &StageResult{
		Success: true,
		Message: "Flow completed successfully",
	}, nil
}

// executeStage 执行单个Stage
func (v *FlowExecutorView) executeStage(binding *FlowStageBinding) (*StageResult, error) {
	startTime := time.Now()

	v.logger.Debug("Executing stage",
		zap.String("stage_name", binding.Stage.GetName()),
		zap.String("stage_uuid", binding.StageUUID.String()))

	// 设置当前Stage
	v.executor.CurrentStage = binding.Stage

	// 执行Stage
	ctx := context.WithValue(context.Background(), "gin_context", v.executor.Request)
	result, err := binding.Stage.Execute(ctx, v.executor)

	duration := time.Since(startTime)

	if err != nil {
		v.recordStageExecution(binding, nil, err)
		return nil, err
	}

	v.logger.Debug("Stage execution completed",
		zap.String("stage_name", binding.Stage.GetName()),
		zap.Bool("success", result.Success),
		zap.Duration("duration", duration))

	return result, nil
}

// evaluateStagePolicy 运行时策略评估
func (v *FlowExecutorView) evaluateStagePolicy(binding *FlowStageBinding) bool {
	// 如果没有策略服务，默认通过
	if v.policyService == nil {
		return true
	}

	// 创建策略请求
	request := &policies.PolicyRequest{
		HTTPRequest: v.executor.Request,
		Context:     v.executor.Plan.Context,
	}

	// 设置用户信息
	if user, exists := v.executor.Plan.GetContext(PlanContextPendingUser); exists {
		request.User = user
	}

	// 评估FlowStageBinding的策略
	mode := policies.PolicyEngineMode(binding.PolicyEngineMode)
	result, err := v.policyService.EvaluatePolicies(
		context.Background(),
		binding.BindingUUID,
		"flow_stage_binding",
		mode,
		request,
	)

	if err != nil {
		v.logger.Error("Failed to evaluate stage policies during execution",
			zap.String("stage_name", binding.Stage.GetName()),
			zap.Error(err))
		return false
	}

	v.logger.Debug("Runtime stage policy evaluation completed",
		zap.String("stage_name", binding.Stage.GetName()),
		zap.Bool("passing", result.Passing),
		zap.Strings("messages", result.Messages))

	return result.Passing
}

// recordStageExecution 记录Stage执行
func (v *FlowExecutorView) recordStageExecution(binding *FlowStageBinding, result *StageResult, err error) {
	record := StageExecutionRecord{
		StageUUID:  binding.StageUUID,
		StageName:  binding.Stage.GetName(),
		ExecutedAt: time.Now(),
		Success:    err == nil && (result == nil || result.Success),
		Duration:   time.Since(v.executor.StartedAt),
	}

	if result != nil {
		record.Message = result.Message
		record.Context = result.Context
	}

	if err != nil {
		record.Message = err.Error()
	}

	v.executor.History = append(v.executor.History, record)
}

// GetExecutor 获取执行器
func (v *FlowExecutorView) GetExecutor() *FlowExecutor {
	return v.executor
}
