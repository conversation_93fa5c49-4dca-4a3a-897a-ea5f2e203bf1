package tenants

import (
	"fmt"
	"strings"
	"time"

	"go-abm-idp/internal/brands"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// TenantService 租户服务
type TenantService struct {
	db           *gorm.DB
	logger       *zap.Logger
	brandService *brands.BrandService
}

// NewTenantService 创建新的租户服务
func NewTenantService(db *gorm.DB, logger *zap.Logger, brandService *brands.BrandService) *TenantService {
	return &TenantService{
		db:           db,
		logger:       logger,
		brandService: brandService,
	}
}

// CreateTenant 创建租户
func (s *TenantService) CreateTenant(tenant *Tenant) error {
	if err := tenant.Validate(); err != nil {
		return fmt.Errorf("tenant validation failed: %w", err)
	}
	
	tenant.TenantUUID = uuid.New()
	tenant.CreatedAt = time.Now()
	tenant.UpdatedAt = time.Now()
	
	// 生成Schema名称
	if tenant.SchemaName == "" {
		tenant.SchemaName = "tenant_" + strings.ReplaceAll(tenant.Slug, "-", "_")
	}
	
	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	
	// 创建租户记录
	if err := tx.Create(tenant).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create tenant: %w", err)
	}
	
	// 创建租户数据库Schema
	if err := s.createTenantSchema(tx, tenant.SchemaName); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create tenant schema: %w", err)
	}
	
	// 创建租户品牌（如果没有指定）
	if tenant.BrandUUID == nil {
		brand, err := s.createTenantBrand(tx, tenant)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to create tenant brand: %w", err)
		}
		tenant.BrandUUID = &brand.BrandUUID
		
		// 更新租户记录
		if err := tx.Save(tenant).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to update tenant with brand: %w", err)
		}
	}
	
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit tenant creation: %w", err)
	}
	
	s.logger.Info("Tenant created",
		zap.String("tenant_uuid", tenant.TenantUUID.String()),
		zap.String("name", tenant.Name),
		zap.String("domain", tenant.Domain),
		zap.String("schema", tenant.SchemaName))
	
	return nil
}

// GetTenantByDomain 根据域名获取租户
func (s *TenantService) GetTenantByDomain(domain string) (*Tenant, error) {
	var tenant Tenant
	
	// 首先尝试精确匹配
	err := s.db.Where("domain = ? AND enabled = ?", domain, true).First(&tenant).Error
	if err == nil {
		return &tenant, nil
	}
	
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to get tenant by domain: %w", err)
	}
	
	// 尝试子域名匹配
	err = s.db.Where("? = ANY(subdomains) AND enabled = ?", domain, true).First(&tenant).Error
	if err == nil {
		return &tenant, nil
	}
	
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to get tenant by subdomain: %w", err)
	}
	
	return nil, fmt.Errorf("tenant not found for domain: %s", domain)
}

// GetTenantByUUID 根据UUID获取租户
func (s *TenantService) GetTenantByUUID(tenantUUID uuid.UUID) (*Tenant, error) {
	var tenant Tenant
	if err := s.db.Where("tenant_uuid = ?", tenantUUID).First(&tenant).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("tenant not found: %s", tenantUUID.String())
		}
		return nil, fmt.Errorf("failed to get tenant: %w", err)
	}
	
	return &tenant, nil
}

// ListTenants 列出所有租户
func (s *TenantService) ListTenants() ([]Tenant, error) {
	var tenants []Tenant
	if err := s.db.Find(&tenants).Error; err != nil {
		return nil, fmt.Errorf("failed to list tenants: %w", err)
	}
	
	return tenants, nil
}

// UpdateTenant 更新租户
func (s *TenantService) UpdateTenant(tenant *Tenant) error {
	if err := tenant.Validate(); err != nil {
		return fmt.Errorf("tenant validation failed: %w", err)
	}
	
	tenant.UpdatedAt = time.Now()
	
	if err := s.db.Save(tenant).Error; err != nil {
		return fmt.Errorf("failed to update tenant: %w", err)
	}
	
	s.logger.Info("Tenant updated",
		zap.String("tenant_uuid", tenant.TenantUUID.String()),
		zap.String("name", tenant.Name))
	
	return nil
}

// DeleteTenant 删除租户
func (s *TenantService) DeleteTenant(tenantUUID uuid.UUID) error {
	tenant, err := s.GetTenantByUUID(tenantUUID)
	if err != nil {
		return err
	}
	
	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	
	// 删除租户相关数据
	if err := s.deleteTenantData(tx, tenant); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to delete tenant data: %w", err)
	}
	
	// 删除租户Schema
	if err := s.dropTenantSchema(tx, tenant.SchemaName); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to drop tenant schema: %w", err)
	}
	
	// 删除租户记录
	if err := tx.Delete(&Tenant{}, "tenant_uuid = ?", tenantUUID).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to delete tenant: %w", err)
	}
	
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit tenant deletion: %w", err)
	}
	
	s.logger.Info("Tenant deleted", zap.String("tenant_uuid", tenantUUID.String()))
	return nil
}

// GetTenantContext 获取租户上下文
func (s *TenantService) GetTenantContext(ctx *gin.Context) (*TenantContext, error) {
	// 从请求获取域名
	domain := ctx.Request.Host
	
	// 获取租户
	tenant, err := s.GetTenantByDomain(domain)
	if err != nil {
		return nil, fmt.Errorf("failed to get tenant for domain %s: %w", domain, err)
	}
	
	// 创建租户上下文
	tenantContext := &TenantContext{
		Tenant:        tenant,
		RequestDomain: domain,
		SchemaName:    tenant.SchemaName,
		QuotaUsage:    tenant.GetQuotaUsage(),
	}
	
	// 设置用户信息
	if user, exists := ctx.Get("user"); exists {
		tenantContext.User = user
		
		// 获取用户在租户中的角色和权限
		if userUUID, ok := user.(uuid.UUID); ok {
			tenantUser, err := s.GetTenantUser(tenant.TenantUUID, userUUID)
			if err == nil {
				tenantContext.Role = tenantUser.Role
				tenantContext.Permissions = tenantUser.Permissions
			}
		}
	}
	
	return tenantContext, nil
}

// AddUserToTenant 添加用户到租户
func (s *TenantService) AddUserToTenant(tenantUUID, userUUID uuid.UUID, role string, permissions []string) error {
	tenantUser := &TenantUser{
		RelationUUID: uuid.New(),
		TenantUUID:   tenantUUID,
		UserUUID:     userUUID,
		Role:         role,
		Permissions:  permissions,
		Status:       "active",
		Enabled:      true,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
	
	if err := s.db.Create(tenantUser).Error; err != nil {
		return fmt.Errorf("failed to add user to tenant: %w", err)
	}
	
	s.logger.Info("User added to tenant",
		zap.String("tenant_uuid", tenantUUID.String()),
		zap.String("user_uuid", userUUID.String()),
		zap.String("role", role))
	
	return nil
}

// RemoveUserFromTenant 从租户移除用户
func (s *TenantService) RemoveUserFromTenant(tenantUUID, userUUID uuid.UUID) error {
	if err := s.db.Delete(&TenantUser{}, "tenant_uuid = ? AND user_uuid = ?", tenantUUID, userUUID).Error; err != nil {
		return fmt.Errorf("failed to remove user from tenant: %w", err)
	}
	
	s.logger.Info("User removed from tenant",
		zap.String("tenant_uuid", tenantUUID.String()),
		zap.String("user_uuid", userUUID.String()))
	
	return nil
}

// GetTenantUser 获取租户用户关系
func (s *TenantService) GetTenantUser(tenantUUID, userUUID uuid.UUID) (*TenantUser, error) {
	var tenantUser TenantUser
	err := s.db.Where("tenant_uuid = ? AND user_uuid = ?", tenantUUID, userUUID).First(&tenantUser).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found in tenant")
		}
		return nil, fmt.Errorf("failed to get tenant user: %w", err)
	}
	
	return &tenantUser, nil
}

// createTenantSchema 创建租户数据库Schema
func (s *TenantService) createTenantSchema(tx *gorm.DB, schemaName string) error {
	// 创建Schema
	if err := tx.Exec(fmt.Sprintf("CREATE SCHEMA IF NOT EXISTS %s", schemaName)).Error; err != nil {
		return fmt.Errorf("failed to create schema: %w", err)
	}
	
	// 在Schema中创建必要的表
	// 这里简化处理，实际应该复制完整的表结构
	tables := []string{
		"users", "groups", "applications", "sources", "providers",
		"flows", "stages", "policies", "events",
	}
	
	for _, table := range tables {
		sql := fmt.Sprintf(`
			CREATE TABLE IF NOT EXISTS %s.%s (
				id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
				created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
				updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
			)`, schemaName, table)
		
		if err := tx.Exec(sql).Error; err != nil {
			return fmt.Errorf("failed to create table %s: %w", table, err)
		}
	}
	
	return nil
}

// dropTenantSchema 删除租户数据库Schema
func (s *TenantService) dropTenantSchema(tx *gorm.DB, schemaName string) error {
	return tx.Exec(fmt.Sprintf("DROP SCHEMA IF EXISTS %s CASCADE", schemaName)).Error
}

// createTenantBrand 创建租户品牌
func (s *TenantService) createTenantBrand(tx *gorm.DB, tenant *Tenant) (*brands.Brand, error) {
	brand := &brands.Brand{
		Domain:        tenant.Domain,
		Default:       false,
		BrandingTitle: tenant.Name,
	}
	
	if err := tx.Create(brand).Error; err != nil {
		return nil, fmt.Errorf("failed to create tenant brand: %w", err)
	}
	
	return brand, nil
}

// deleteTenantData 删除租户相关数据
func (s *TenantService) deleteTenantData(tx *gorm.DB, tenant *Tenant) error {
	// 删除租户用户关系
	if err := tx.Delete(&TenantUser{}, "tenant_uuid = ?", tenant.TenantUUID).Error; err != nil {
		return fmt.Errorf("failed to delete tenant users: %w", err)
	}
	
	// 删除租户设置
	if err := tx.Delete(&TenantSettings{}, "tenant_uuid = ?", tenant.TenantUUID).Error; err != nil {
		return fmt.Errorf("failed to delete tenant settings: %w", err)
	}
	
	// 删除租户审计日志
	if err := tx.Delete(&TenantAuditLog{}, "tenant_uuid = ?", tenant.TenantUUID).Error; err != nil {
		return fmt.Errorf("failed to delete tenant audit logs: %w", err)
	}
	
	// 删除租户指标
	if err := tx.Delete(&TenantMetrics{}, "tenant_uuid = ?", tenant.TenantUUID).Error; err != nil {
		return fmt.Errorf("failed to delete tenant metrics: %w", err)
	}
	
	return nil
}

// CreateDefaultTenant 创建默认租户
func (s *TenantService) CreateDefaultTenant() error {
	// 检查是否已存在默认租户
	_, err := s.GetTenantByDomain("localhost")
	if err == nil {
		return nil // 默认租户已存在
	}
	
	// 创建默认租户
	defaultTenant := &Tenant{
		Name:        "Default Tenant",
		Slug:        "default",
		Description: "Default tenant for single-tenant mode",
		Domain:      "localhost",
		SchemaName:  "public", // 使用默认的public schema
		Status:      TenantStatusActive,
		Enabled:     true,
		PlanType:    "enterprise",
		AdminEmail:  "admin@localhost",
	}
	
	if err := s.CreateTenant(defaultTenant); err != nil {
		return fmt.Errorf("failed to create default tenant: %w", err)
	}
	
	s.logger.Info("Default tenant created successfully")
	return nil
}
