package events

import (
	"fmt"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// EventService 事件服务
type EventService struct {
	db     *gorm.DB
	logger *zap.Logger

	// 事件传输器
	transports []EventTransport

	// 事件规则缓存
	rules    []EventRule
	rulesMux sync.RWMutex

	// 事件队列
	eventQueue chan *Event

	// 服务状态
	running bool
	stopCh  chan struct{}
}

// NewEventService 创建新的事件服务
func NewEventService(db *gorm.DB, logger *zap.Logger) *EventService {
	service := &EventService{
		db:         db,
		logger:     logger,
		transports: make([]EventTransport, 0),
		rules:      make([]EventRule, 0),
		eventQueue: make(chan *Event, 1000), // 缓冲1000个事件
		stopCh:     make(chan struct{}),
	}

	// 加载事件规则
	service.loadEventRules()

	return service
}

// Start 启动事件服务
func (s *EventService) Start() error {
	if s.running {
		return fmt.Errorf("event service is already running")
	}

	s.running = true

	// 启动事件处理协程
	go s.processEvents()

	// 启动规则刷新协程
	go s.refreshRules()

	s.logger.Info("Event service started")
	return nil
}

// Stop 停止事件服务
func (s *EventService) Stop() error {
	if !s.running {
		return fmt.Errorf("event service is not running")
	}

	s.running = false
	close(s.stopCh)

	s.logger.Info("Event service stopped")
	return nil
}

// CreateEvent 创建事件
func (s *EventService) CreateEvent(action EventAction, message string, level string) *Event {
	event := &Event{
		EventUUID: uuid.New(),
		Action:    action,
		Message:   message,
		Level:     level,
		Context:   make(map[string]interface{}),
		CreatedAt: time.Now(),
	}

	return event
}

// EmitEvent 发送事件
func (s *EventService) EmitEvent(event *Event) error {
	// 保存事件到数据库
	if err := s.db.Create(event).Error; err != nil {
		s.logger.Error("Failed to save event to database",
			zap.String("event_uuid", event.EventUUID.String()),
			zap.Error(err))
		return fmt.Errorf("failed to save event: %w", err)
	}

	// 将事件加入处理队列
	select {
	case s.eventQueue <- event:
		// 事件已加入队列
	default:
		// 队列已满，记录警告
		s.logger.Warn("Event queue is full, dropping event",
			zap.String("event_uuid", event.EventUUID.String()),
			zap.String("action", string(event.Action)))
	}

	return nil
}

// EmitFlowEvent 发送Flow相关事件
func (s *EventService) EmitFlowEvent(ctx *gin.Context, action EventAction, flowUUID *uuid.UUID, stageUUID *uuid.UUID, userUUID *uuid.UUID, message string, context map[string]interface{}) error {
	event := s.CreateEvent(action, message, EventLevelInfo)

	// 设置关联对象
	event.FlowUUID = flowUUID
	event.StageUUID = stageUUID
	event.UserUUID = userUUID

	// 设置请求信息
	if ctx != nil {
		event.ClientIP = ctx.ClientIP()
		event.UserAgent = ctx.GetHeader("User-Agent")
	}

	// 设置上下文
	if context != nil {
		for key, value := range context {
			event.SetContext(key, value)
		}
	}

	return s.EmitEvent(event)
}

// EmitAuthEvent 发送认证相关事件
func (s *EventService) EmitAuthEvent(ctx *gin.Context, action EventAction, userUUID *uuid.UUID, message string, success bool, context map[string]interface{}) error {
	level := EventLevelInfo
	if !success {
		level = EventLevelWarning
	}

	event := s.CreateEvent(action, message, level)
	event.UserUUID = userUUID

	// 设置请求信息
	if ctx != nil {
		event.ClientIP = ctx.ClientIP()
		event.UserAgent = ctx.GetHeader("User-Agent")
	}

	// 设置上下文
	event.SetContext("success", success)
	if context != nil {
		for key, value := range context {
			event.SetContext(key, value)
		}
	}

	return s.EmitEvent(event)
}

// EmitAppEvent 发送应用相关事件
func (s *EventService) EmitAppEvent(ctx *gin.Context, action EventAction, appUUID *uuid.UUID, userUUID *uuid.UUID, message string, context map[string]interface{}) error {
	event := s.CreateEvent(action, message, EventLevelInfo)

	event.ApplicationUUID = appUUID
	event.UserUUID = userUUID

	// 设置请求信息
	if ctx != nil {
		event.ClientIP = ctx.ClientIP()
		event.UserAgent = ctx.GetHeader("User-Agent")
	}

	// 设置上下文
	if context != nil {
		for key, value := range context {
			event.SetContext(key, value)
		}
	}

	return s.EmitEvent(event)
}

// processEvents 处理事件队列
func (s *EventService) processEvents() {
	for {
		select {
		case event := <-s.eventQueue:
			s.handleEvent(event)
		case <-s.stopCh:
			return
		}
	}
}

// handleEvent 处理单个事件
func (s *EventService) handleEvent(event *Event) {
	s.rulesMux.RLock()
	rules := make([]EventRule, len(s.rules))
	copy(rules, s.rules)
	s.rulesMux.RUnlock()

	// 检查匹配的规则
	for _, rule := range rules {
		if rule.Matches(event) {
			s.executeRule(&rule, event)
		}
	}

	// 发送到所有启用的传输器
	for _, transport := range s.transports {
		if transport.IsEnabled() {
			if err := transport.Send(event); err != nil {
				s.logger.Error("Failed to send event via transport",
					zap.String("transport", transport.GetName()),
					zap.String("event_uuid", event.EventUUID.String()),
					zap.Error(err))
			}
		}
	}
}

// executeRule 执行事件规则
func (s *EventService) executeRule(rule *EventRule, event *Event) {
	// 更新规则触发统计
	now := time.Now()
	rule.LastTriggered = &now
	rule.TriggerCount++

	// 保存规则统计到数据库
	s.db.Model(rule).Updates(map[string]interface{}{
		"last_triggered": rule.LastTriggered,
		"trigger_count":  rule.TriggerCount,
	})

	// 根据动作类型执行相应操作
	switch rule.ActionType {
	case "notification":
		s.createNotification(rule, event)
	case "webhook":
		s.sendWebhook(rule, event)
	case "script":
		s.executeScript(rule, event)
	default:
		s.logger.Warn("Unknown rule action type",
			zap.String("rule_name", rule.Name),
			zap.String("action_type", rule.ActionType))
	}
}

// createNotification 创建通知
func (s *EventService) createNotification(rule *EventRule, event *Event) {
	// 从规则配置获取通知目标
	targetType, _ := rule.ActionConfig["target_type"].(string)
	targetUUIDStr, _ := rule.ActionConfig["target_uuid"].(string)

	if targetType == "" || targetUUIDStr == "" {
		s.logger.Warn("Invalid notification configuration",
			zap.String("rule_name", rule.Name))
		return
	}

	targetUUID, err := uuid.Parse(targetUUIDStr)
	if err != nil {
		s.logger.Warn("Invalid target UUID in notification configuration",
			zap.String("rule_name", rule.Name),
			zap.String("target_uuid", targetUUIDStr))
		return
	}

	notification := &EventNotification{
		NotificationUUID: uuid.New(),
		EventUUID:        event.EventUUID,
		TargetType:       targetType,
		TargetUUID:       targetUUID,
		Status:           EventStatusPending,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	if err := s.db.Create(notification).Error; err != nil {
		s.logger.Error("Failed to create event notification",
			zap.String("rule_name", rule.Name),
			zap.Error(err))
	}
}

// sendWebhook 发送Webhook
func (s *EventService) sendWebhook(rule *EventRule, event *Event) {
	// 简化的Webhook发送实现
	s.logger.Info("Webhook triggered by event rule",
		zap.String("rule_name", rule.Name),
		zap.String("event_uuid", event.EventUUID.String()))
}

// executeScript 执行脚本
func (s *EventService) executeScript(rule *EventRule, event *Event) {
	// 简化的脚本执行实现
	s.logger.Info("Script triggered by event rule",
		zap.String("rule_name", rule.Name),
		zap.String("event_uuid", event.EventUUID.String()))
}

// refreshRules 刷新事件规则
func (s *EventService) refreshRules() {
	ticker := time.NewTicker(5 * time.Minute) // 每5分钟刷新一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.loadEventRules()
		case <-s.stopCh:
			return
		}
	}
}

// loadEventRules 加载事件规则
func (s *EventService) loadEventRules() {
	var rules []EventRule
	if err := s.db.Where("enabled = ?", true).Find(&rules).Error; err != nil {
		s.logger.Error("Failed to load event rules", zap.Error(err))
		return
	}

	s.rulesMux.Lock()
	s.rules = rules
	s.rulesMux.Unlock()

	s.logger.Debug("Event rules loaded", zap.Int("count", len(rules)))
}

// AddTransport 添加事件传输器
func (s *EventService) AddTransport(transport EventTransport) {
	s.transports = append(s.transports, transport)
	s.logger.Info("Event transport added", zap.String("name", transport.GetName()))
}

// ListEvents 列出事件
func (s *EventService) ListEvents(limit, offset int, filters map[string]interface{}) ([]Event, int64, error) {
	var events []Event
	var total int64

	query := s.db.Model(&Event{})

	// 应用过滤条件
	if action, exists := filters["action"]; exists {
		query = query.Where("action = ?", action)
	}
	if level, exists := filters["level"]; exists {
		query = query.Where("level = ?", level)
	}
	if userUUID, exists := filters["user_uuid"]; exists {
		query = query.Where("user_uuid = ?", userUUID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count events: %w", err)
	}

	// 获取事件列表
	if err := query.Order("created_at DESC").Limit(limit).Offset(offset).Find(&events).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list events: %w", err)
	}

	return events, total, nil
}

// CleanupExpiredEvents 清理过期事件
func (s *EventService) CleanupExpiredEvents() error {
	result := s.db.Where("expires_at IS NOT NULL AND expires_at < ?", time.Now()).Delete(&Event{})
	if result.Error != nil {
		return fmt.Errorf("failed to cleanup expired events: %w", result.Error)
	}

	s.logger.Info("Expired events cleaned up", zap.Int64("count", result.RowsAffected))
	return nil
}
