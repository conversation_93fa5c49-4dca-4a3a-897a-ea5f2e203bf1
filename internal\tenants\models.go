package tenants

import (
	"fmt"
	"time"

	"github.com/google/uuid"
)

// Tenant 租户模型，用于企业级多租户数据隔离
type Tenant struct {
	TenantUUID uuid.UUID `json:"tenant_uuid" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	
	// 基本信息
	Name        string `json:"name" gorm:"uniqueIndex;not null"`
	Slug        string `json:"slug" gorm:"uniqueIndex;not null"`
	Description string `json:"description"`
	
	// 域名配置
	Domain       string   `json:"domain" gorm:"uniqueIndex;not null"`
	Subdomains   []string `json:"subdomains" gorm:"type:text[]"`
	
	// 数据库配置
	SchemaName   string `json:"schema_name" gorm:"uniqueIndex;not null"`
	DatabaseURL  string `json:"database_url"`
	
	// 状态信息
	Status    TenantStatus `json:"status" gorm:"default:'active'"`
	Enabled   bool         `json:"enabled" gorm:"default:true"`
	
	// 配额限制
	MaxUsers        int `json:"max_users" gorm:"default:1000"`
	MaxApplications int `json:"max_applications" gorm:"default:100"`
	MaxSources      int `json:"max_sources" gorm:"default:50"`
	MaxProviders    int `json:"max_providers" gorm:"default:50"`
	MaxFlows        int `json:"max_flows" gorm:"default:100"`
	
	// 存储配额（字节）
	StorageQuota int64 `json:"storage_quota" gorm:"default:**********"` // 1GB
	StorageUsed  int64 `json:"storage_used" gorm:"default:0"`
	
	// 品牌关联
	BrandUUID *uuid.UUID `json:"brand_uuid,omitempty"`
	
	// 管理员信息
	AdminUserUUID *uuid.UUID `json:"admin_user_uuid,omitempty"`
	AdminEmail    string     `json:"admin_email"`
	
	// 计费信息
	PlanType      string     `json:"plan_type" gorm:"default:'free'"`
	BillingEmail  string     `json:"billing_email"`
	TrialEndsAt   *time.Time `json:"trial_ends_at,omitempty"`
	
	// 时间戳
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// 关联对象
	Brand interface{} `json:"brand,omitempty" gorm:"-"`
}

// TenantStatus 租户状态枚举
type TenantStatus string

const (
	TenantStatusActive    TenantStatus = "active"
	TenantStatusInactive  TenantStatus = "inactive"
	TenantStatusSuspended TenantStatus = "suspended"
	TenantStatusArchived  TenantStatus = "archived"
)

// GetUUID 返回租户的UUID
func (t *Tenant) GetUUID() uuid.UUID {
	return t.TenantUUID
}

// GetName 返回租户的名称
func (t *Tenant) GetName() string {
	return t.Name
}

// GetSlug 返回租户的Slug
func (t *Tenant) GetSlug() string {
	return t.Slug
}

// GetDomain 返回租户的域名
func (t *Tenant) GetDomain() string {
	return t.Domain
}

// GetSchemaName 返回租户的数据库Schema名称
func (t *Tenant) GetSchemaName() string {
	return t.SchemaName
}

// IsActive 检查租户是否活跃
func (t *Tenant) IsActive() bool {
	return t.Status == TenantStatusActive && t.Enabled
}

// IsTrialExpired 检查试用是否过期
func (t *Tenant) IsTrialExpired() bool {
	if t.TrialEndsAt == nil {
		return false
	}
	return time.Now().After(*t.TrialEndsAt)
}

// Validate 验证租户配置
func (t *Tenant) Validate() error {
	if t.Name == "" {
		return fmt.Errorf("tenant name cannot be empty")
	}
	if t.Slug == "" {
		return fmt.Errorf("tenant slug cannot be empty")
	}
	if t.Domain == "" {
		return fmt.Errorf("tenant domain cannot be empty")
	}
	if t.SchemaName == "" {
		return fmt.Errorf("tenant schema name cannot be empty")
	}
	return nil
}

// GetQuotaUsage 获取配额使用情况
func (t *Tenant) GetQuotaUsage() map[string]interface{} {
	return map[string]interface{}{
		"storage": map[string]interface{}{
			"quota": t.StorageQuota,
			"used":  t.StorageUsed,
			"percentage": float64(t.StorageUsed) / float64(t.StorageQuota) * 100,
		},
		"users": map[string]interface{}{
			"max": t.MaxUsers,
		},
		"applications": map[string]interface{}{
			"max": t.MaxApplications,
		},
		"sources": map[string]interface{}{
			"max": t.MaxSources,
		},
		"providers": map[string]interface{}{
			"max": t.MaxProviders,
		},
		"flows": map[string]interface{}{
			"max": t.MaxFlows,
		},
	}
}

// TenantSettings 租户设置
type TenantSettings struct {
	SettingUUID uuid.UUID `json:"setting_uuid" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	
	// 关联的租户
	TenantUUID uuid.UUID `json:"tenant_uuid" gorm:"not null"`
	
	// 设置信息
	Key         string      `json:"key" gorm:"not null"`
	Value       interface{} `json:"value" gorm:"type:jsonb"`
	Description string      `json:"description"`
	
	// 设置类型
	SettingType string `json:"setting_type" gorm:"default:'string'"` // string, number, boolean, json
	
	// 设置分类
	Category string `json:"category" gorm:"default:'general'"` // general, security, branding, integration
	
	// 是否可见
	Visible bool `json:"visible" gorm:"default:true"`
	
	// 时间戳
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// 关联对象
	Tenant Tenant `json:"tenant" gorm:"foreignKey:TenantUUID"`
}

// TenantUser 租户用户关系
type TenantUser struct {
	RelationUUID uuid.UUID `json:"relation_uuid" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	
	// 关联的租户和用户
	TenantUUID uuid.UUID `json:"tenant_uuid" gorm:"not null"`
	UserUUID   uuid.UUID `json:"user_uuid" gorm:"not null"`
	
	// 用户角色
	Role string `json:"role" gorm:"default:'user'"` // admin, user, viewer
	
	// 权限
	Permissions []string `json:"permissions" gorm:"type:text[]"`
	
	// 状态
	Status  string `json:"status" gorm:"default:'active'"` // active, inactive, suspended
	Enabled bool   `json:"enabled" gorm:"default:true"`
	
	// 时间戳
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// 关联对象
	Tenant Tenant `json:"tenant" gorm:"foreignKey:TenantUUID"`
}

// TenantAuditLog 租户审计日志
type TenantAuditLog struct {
	LogUUID uuid.UUID `json:"log_uuid" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	
	// 关联的租户
	TenantUUID uuid.UUID `json:"tenant_uuid" gorm:"not null"`
	
	// 操作用户
	UserUUID *uuid.UUID `json:"user_uuid,omitempty"`
	
	// 事件信息
	EventType   string `json:"event_type" gorm:"not null"`
	EventAction string `json:"event_action" gorm:"not null"`
	
	// 事件详情
	Details map[string]interface{} `json:"details" gorm:"type:jsonb"`
	
	// 请求信息
	ClientIP  string `json:"client_ip"`
	UserAgent string `json:"user_agent"`
	
	// 结果
	Success bool   `json:"success"`
	Message string `json:"message"`
	
	// 时间戳
	CreatedAt time.Time `json:"created_at"`
	
	// 关联对象
	Tenant Tenant `json:"tenant" gorm:"foreignKey:TenantUUID"`
}

// TenantMetrics 租户指标
type TenantMetrics struct {
	MetricUUID uuid.UUID `json:"metric_uuid" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	
	// 关联的租户
	TenantUUID uuid.UUID `json:"tenant_uuid" gorm:"not null"`
	
	// 指标类型和时间
	MetricType string    `json:"metric_type" gorm:"not null"`
	MetricDate time.Time `json:"metric_date" gorm:"not null"`
	
	// 指标值
	Value      float64                `json:"value"`
	Attributes map[string]interface{} `json:"attributes" gorm:"type:jsonb"`
	
	// 时间戳
	CreatedAt time.Time `json:"created_at"`
	
	// 关联对象
	Tenant Tenant `json:"tenant" gorm:"foreignKey:TenantUUID"`
}

// TenantContext 租户上下文
type TenantContext struct {
	Tenant *Tenant `json:"tenant"`
	
	// 运行时信息
	RequestDomain string `json:"request_domain"`
	SchemaName    string `json:"schema_name"`
	
	// 用户信息
	User interface{} `json:"user,omitempty"`
	
	// 权限信息
	Permissions []string `json:"permissions,omitempty"`
	Role        string   `json:"role,omitempty"`
	
	// 配额信息
	QuotaUsage map[string]interface{} `json:"quota_usage,omitempty"`
}

// TenantPlan 租户计划
type TenantPlan struct {
	PlanUUID uuid.UUID `json:"plan_uuid" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	
	// 计划信息
	Name        string `json:"name" gorm:"uniqueIndex;not null"`
	Description string `json:"description"`
	
	// 计划类型
	PlanType string `json:"plan_type" gorm:"not null"` // free, basic, premium, enterprise
	
	// 配额限制
	MaxUsers        int   `json:"max_users"`
	MaxApplications int   `json:"max_applications"`
	MaxSources      int   `json:"max_sources"`
	MaxProviders    int   `json:"max_providers"`
	MaxFlows        int   `json:"max_flows"`
	StorageQuota    int64 `json:"storage_quota"`
	
	// 功能特性
	Features []string `json:"features" gorm:"type:text[]"`
	
	// 价格信息
	Price         float64 `json:"price"`
	Currency      string  `json:"currency" gorm:"default:'USD'"`
	BillingCycle  string  `json:"billing_cycle" gorm:"default:'monthly'"` // monthly, yearly
	
	// 试用期
	TrialDays int `json:"trial_days" gorm:"default:30"`
	
	// 是否启用
	Enabled bool `json:"enabled" gorm:"default:true"`
	
	// 时间戳
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
