package brands

import (
	"fmt"
	"time"

	"github.com/google/uuid"
)

// Brand 品牌模型，用于多租户UI定制和Flow配置
type Brand struct {
	BrandUUID uuid.UUID `json:"brand_uuid" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	
	// 基本信息
	Domain  string `json:"domain" gorm:"uniqueIndex;not null"`
	Default bool   `json:"default" gorm:"default:false"`
	
	// 品牌定制
	BrandingTitle     string `json:"branding_title" gorm:"default:'authentik'"`
	BrandingLogo      string `json:"branding_logo"`
	BrandingFavicon   string `json:"branding_favicon"`
	BrandingCustomCSS string `json:"branding_custom_css"`
	
	// Flow配置 - 关联到不同用途的Flow
	FlowAuthenticationUUID *uuid.UUID `json:"flow_authentication_uuid,omitempty"`
	FlowInvalidationUUID   *uuid.UUID `json:"flow_invalidation_uuid,omitempty"`
	FlowRecoveryUUID       *uuid.UUID `json:"flow_recovery_uuid,omitempty"`
	FlowUnenrollmentUUID   *uuid.UUID `json:"flow_unenrollment_uuid,omitempty"`
	FlowUserSettingsUUID   *uuid.UUID `json:"flow_user_settings_uuid,omitempty"`
	FlowDeviceCodeUUID     *uuid.UUID `json:"flow_device_code_uuid,omitempty"`
	
	// 用户界面配置
	WebCertificate         *uuid.UUID `json:"web_certificate,omitempty"`
	AttributesUsername     string     `json:"attributes_username" gorm:"default:'username'"`
	AttributesEmail        string     `json:"attributes_email" gorm:"default:'email'"`
	AttributesName         string     `json:"attributes_name" gorm:"default:'name'"`
	AttributesGroup        string     `json:"attributes_group" gorm:"default:'groups'"`
	
	// 默认设置
	DefaultApplicationUUID *uuid.UUID `json:"default_application_uuid,omitempty"`
	DefaultSourceUUID      *uuid.UUID `json:"default_source_uuid,omitempty"`
	
	// 时间戳
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// GetUUID 返回品牌的UUID
func (b *Brand) GetUUID() uuid.UUID {
	return b.BrandUUID
}

// GetDomain 返回品牌的域名
func (b *Brand) GetDomain() string {
	return b.Domain
}

// IsDefault 检查是否为默认品牌
func (b *Brand) IsDefault() bool {
	return b.Default
}

// Validate 验证品牌配置
func (b *Brand) Validate() error {
	if b.Domain == "" {
		return fmt.Errorf("brand domain cannot be empty")
	}
	if b.BrandingTitle == "" {
		return fmt.Errorf("branding title cannot be empty")
	}
	return nil
}

// GetBrandingConfig 获取品牌配置
func (b *Brand) GetBrandingConfig() map[string]interface{} {
	return map[string]interface{}{
		"title":      b.BrandingTitle,
		"logo":       b.BrandingLogo,
		"favicon":    b.BrandingFavicon,
		"custom_css": b.BrandingCustomCSS,
	}
}

// GetFlowConfig 获取Flow配置
func (b *Brand) GetFlowConfig() map[string]*uuid.UUID {
	return map[string]*uuid.UUID{
		"authentication": b.FlowAuthenticationUUID,
		"invalidation":   b.FlowInvalidationUUID,
		"recovery":       b.FlowRecoveryUUID,
		"unenrollment":   b.FlowUnenrollmentUUID,
		"user_settings":  b.FlowUserSettingsUUID,
		"device_code":    b.FlowDeviceCodeUUID,
	}
}

// BrandContext 品牌上下文，用于模板渲染
type BrandContext struct {
	Brand *Brand `json:"brand"`
	
	// 运行时信息
	RequestDomain string `json:"request_domain"`
	UserAgent     string `json:"user_agent"`
	ClientIP      string `json:"client_ip"`
	
	// 用户信息
	User interface{} `json:"user,omitempty"`
	
	// Flow信息
	CurrentFlow interface{} `json:"current_flow,omitempty"`
	
	// 应用信息
	Application interface{} `json:"application,omitempty"`
}

// BrandSettings 品牌设置
type BrandSettings struct {
	SettingUUID uuid.UUID `json:"setting_uuid" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	
	// 关联的品牌
	BrandUUID uuid.UUID `json:"brand_uuid" gorm:"not null"`
	
	// 设置信息
	Key         string      `json:"key" gorm:"not null"`
	Value       interface{} `json:"value" gorm:"type:jsonb"`
	Description string      `json:"description"`
	
	// 设置类型
	SettingType string `json:"setting_type" gorm:"default:'string'"` // string, number, boolean, json
	
	// 是否可见
	Visible bool `json:"visible" gorm:"default:true"`
	
	// 时间戳
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// 关联对象
	Brand Brand `json:"brand" gorm:"foreignKey:BrandUUID"`
}

// BrandTheme 品牌主题
type BrandTheme struct {
	ThemeUUID uuid.UUID `json:"theme_uuid" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	
	// 关联的品牌
	BrandUUID uuid.UUID `json:"brand_uuid" gorm:"not null"`
	
	// 主题信息
	Name        string `json:"name" gorm:"not null"`
	Description string `json:"description"`
	
	// 主题配置
	PrimaryColor   string `json:"primary_color" gorm:"default:'#1976d2'"`
	SecondaryColor string `json:"secondary_color" gorm:"default:'#dc004e'"`
	BackgroundColor string `json:"background_color" gorm:"default:'#fafafa'"`
	SurfaceColor   string `json:"surface_color" gorm:"default:'#ffffff'"`
	ErrorColor     string `json:"error_color" gorm:"default:'#f44336'"`
	WarningColor   string `json:"warning_color" gorm:"default:'#ff9800'"`
	InfoColor      string `json:"info_color" gorm:"default:'#2196f3'"`
	SuccessColor   string `json:"success_color" gorm:"default:'#4caf50'"`
	
	// 字体配置
	FontFamily string `json:"font_family" gorm:"default:'Roboto, sans-serif'"`
	FontSize   string `json:"font_size" gorm:"default:'14px'"`
	
	// 布局配置
	BorderRadius string `json:"border_radius" gorm:"default:'4px'"`
	Spacing      string `json:"spacing" gorm:"default:'8px'"`
	
	// 自定义CSS
	CustomCSS string `json:"custom_css"`
	
	// 是否启用
	Enabled bool `json:"enabled" gorm:"default:true"`
	
	// 时间戳
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// 关联对象
	Brand Brand `json:"brand" gorm:"foreignKey:BrandUUID"`
}

// GenerateCSS 生成主题CSS
func (t *BrandTheme) GenerateCSS() string {
	css := fmt.Sprintf(`
:root {
  --ak-primary-color: %s;
  --ak-secondary-color: %s;
  --ak-background-color: %s;
  --ak-surface-color: %s;
  --ak-error-color: %s;
  --ak-warning-color: %s;
  --ak-info-color: %s;
  --ak-success-color: %s;
  --ak-font-family: %s;
  --ak-font-size: %s;
  --ak-border-radius: %s;
  --ak-spacing: %s;
}

body {
  font-family: var(--ak-font-family);
  font-size: var(--ak-font-size);
  background-color: var(--ak-background-color);
}

.ak-brand-primary {
  color: var(--ak-primary-color);
}

.ak-brand-secondary {
  color: var(--ak-secondary-color);
}

.ak-brand-surface {
  background-color: var(--ak-surface-color);
  border-radius: var(--ak-border-radius);
}
`,
		t.PrimaryColor,
		t.SecondaryColor,
		t.BackgroundColor,
		t.SurfaceColor,
		t.ErrorColor,
		t.WarningColor,
		t.InfoColor,
		t.SuccessColor,
		t.FontFamily,
		t.FontSize,
		t.BorderRadius,
		t.Spacing,
	)
	
	if t.CustomCSS != "" {
		css += "\n" + t.CustomCSS
	}
	
	return css
}

// BrandAsset 品牌资源
type BrandAsset struct {
	AssetUUID uuid.UUID `json:"asset_uuid" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	
	// 关联的品牌
	BrandUUID uuid.UUID `json:"brand_uuid" gorm:"not null"`
	
	// 资源信息
	Name        string `json:"name" gorm:"not null"`
	Type        string `json:"type" gorm:"not null"` // logo, favicon, background, icon
	MimeType    string `json:"mime_type"`
	Size        int64  `json:"size"`
	
	// 资源内容
	Content []byte `json:"content"`
	URL     string `json:"url"`
	
	// 资源属性
	Width  int `json:"width"`
	Height int `json:"height"`
	
	// 时间戳
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// 关联对象
	Brand Brand `json:"brand" gorm:"foreignKey:BrandUUID"`
}

// BrandLocalization 品牌本地化
type BrandLocalization struct {
	LocalizationUUID uuid.UUID `json:"localization_uuid" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	
	// 关联的品牌
	BrandUUID uuid.UUID `json:"brand_uuid" gorm:"not null"`
	
	// 本地化信息
	Locale      string `json:"locale" gorm:"not null"` // en, zh-CN, ja, etc.
	Language    string `json:"language" gorm:"not null"`
	Country     string `json:"country"`
	
	// 本地化内容
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Messages    map[string]interface{} `json:"messages" gorm:"type:jsonb"`
	
	// 是否启用
	Enabled bool `json:"enabled" gorm:"default:true"`
	
	// 时间戳
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// 关联对象
	Brand Brand `json:"brand" gorm:"foreignKey:BrandUUID"`
}

// BrandStatus 品牌状态枚举
type BrandStatus string

const (
	BrandStatusActive   BrandStatus = "active"
	BrandStatusInactive BrandStatus = "inactive"
	BrandStatusArchived BrandStatus = "archived"
)

// BrandType 品牌类型枚举
type BrandType string

const (
	BrandTypeDefault    BrandType = "default"
	BrandTypeCustom     BrandType = "custom"
	BrandTypeEnterprise BrandType = "enterprise"
)
