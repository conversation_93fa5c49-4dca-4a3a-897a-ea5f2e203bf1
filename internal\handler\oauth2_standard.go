package handler

import (
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"

	"go-abm-idp/internal/oauth2"
	"go-abm-idp/internal/service"

	"github.com/gin-gonic/gin"
)

// OAuth2StandardHandler 标准OAuth2端点处理
// 处理符合OAuth2和OpenID Connect规范的标准端
type OAuth2StandardHandler struct {
	*Handler
	oauth2Service service.OAuth2AdminService
	oidcService   service.OIDCService
	tokenHandler  *oauth2.TokenHandler
}

// NewOAuth2StandardHandler 创建标准OAuth2处理器
func NewOAuth2StandardHandler(
	handler *Handler,
	oauth2Service service.OAuth2AdminService,
	oidcService service.OIDCService,
) *OAuth2StandardHandler {
	return &OAuth2StandardHandler{
		Handler:       handler,
		oauth2Service: oauth2Service,
		oidcService:   oidcService,
		tokenHandler:  nil, // 延迟初始化,等待依赖完善
	}
}

// NewOAuth2StandardHandlerWithTokenHandler 创建带有完整TokenHandler的OAuth2处理器
// 当所有依赖都实现后使用此构造函数
func NewOAuth2StandardHandlerWithTokenHandler(
	handler *Handler,
	oauth2Service service.OAuth2AdminService,
	oidcService service.OIDCService,
	jwtManager *oauth2.JWTManager,
	db interface{}, // 数据库连接,类型待定
) *OAuth2StandardHandler {
	// 暂时创建一个简化的TokenHandler,不依赖具体的数据库类型
	tokenHandler := oauth2.NewTokenHandler(nil, jwtManager)

	return &OAuth2StandardHandler{
		Handler:       handler,
		oauth2Service: oauth2Service,
		oidcService:   oidcService,
		tokenHandler:  tokenHandler,
	}
}

// OpenIDConfiguration godoc
// @Summary OpenID Connect Discovery端点
// @Schemes
// @Description 返回OpenID Connect Provider的配置信息,符合OpenID Connect Discovery 1.0规范
// @Tags OpenID Connect
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "OpenID Connect配置"
// @Router /.well-known/openid_configuration [get]
func (h *OAuth2StandardHandler) OpenIDConfiguration(ctx *gin.Context) {
	config, err := h.oidcService.GetOpenIDConfiguration(ctx.Request.Context(), ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":             "server_error",
			"error_description": "Failed to generate OpenID configuration",
		})
		return
	}

	// OpenID Connect Discovery端点直接返回JSON,不使用标准API响应格式
	ctx.Header("Content-Type", "application/json")
	ctx.Header("Cache-Control", "public, max-age=3600") // 缓存1小时
	ctx.JSON(http.StatusOK, config)
}

// UserInfo godoc
// @Summary 用户信息端点
// @Schemes
// @Description 返回当前认证用户的信息,符合OpenID Connect Core 1.0规范
// @Tags OpenID Connect
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} map[string]interface{} "用户信息"
// @Failure 401 {object} map[string]interface{} "未授

// @Failure 403 {object} map[string]interface{} "权限不足"
// @Router /oauth2/userinfo [get]
func (h *OAuth2StandardHandler) UserInfo(ctx *gin.Context) {
	// 从Authorization header获取访问令牌
	authHeader := ctx.GetHeader("Authorization")
	if authHeader == "" {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"error":             "invalid_request",
			"error_description": "Missing Authorization header",
		})
		return
	}

	// 检查Bearer token格式
	if !strings.HasPrefix(authHeader, "Bearer ") {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"error":             "invalid_request",
			"error_description": "Invalid Authorization header format",
		})
		return
	}

	accessToken := strings.TrimPrefix(authHeader, "Bearer ")
	if accessToken == "" {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"error":             "invalid_token",
			"error_description": "Missing access token",
		})
		return
	}

	// 获取用户信息
	userInfo, err := h.oidcService.GetUserInfo(ctx, accessToken)
	if err != nil {
		// 根据错误类型返回不同的HTTP状态码
		if strings.Contains(err.Error(), "invalid_token") || strings.Contains(err.Error(), "invalid access token") {
			ctx.JSON(http.StatusUnauthorized, gin.H{
				"error":             "invalid_token",
				"error_description": "The access token is invalid or expired",
			})
		} else if strings.Contains(err.Error(), "insufficient_scope") {
			ctx.JSON(http.StatusForbidden, gin.H{
				"error":             "insufficient_scope",
				"error_description": "The access token does not have sufficient scope",
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"error":             "server_error",
				"error_description": "Failed to retrieve user information",
			})
		}
		return
	}

	// UserInfo端点直接返回用户信息JSON,不使用标准API响应格式
	ctx.Header("Content-Type", "application/json")
	ctx.Header("Cache-Control", "no-store") // 用户信息不应被缓
	ctx.JSON(http.StatusOK, userInfo)
}

// getJWTVerificationService 获取JWT验证服务实例
func (h *OAuth2StandardHandler) getJWTVerificationService() *service.JWTVerificationService {
	// 创建JWT验证服务实例
	issuer := h.getIssuerURL()
	// 这里需要从依赖注入中获取db实例,暂时返回nil
	// 在实际使用中,应该在Handler结构中添加db字段或通过其他方式获取
	return service.NewJWTVerificationService(nil, h.Handler.logger, issuer)
}

// getIssuerURL 获取发行者URL
func (h *OAuth2StandardHandler) getIssuerURL() string {
	// 这里应该从配置中获取,暂时硬编码
	return "http://localhost:8000"
}

// getCurrentUserID 获取当前用户ID
func (h *OAuth2StandardHandler) getCurrentUserID(ctx *gin.Context) string {
	// 首先检查context中的用户信息
	if userID, exists := ctx.Get("user_id"); exists {
		if uid, ok := userID.(string); ok && uid != "" {
			return uid
		}
	}

	// 然后检查Cookie中的用户信息
	if userID, err := ctx.Cookie("user_id"); err == nil && userID != "" {
		// 将Cookie中的用户信息设置到context
		ctx.Set("user_id", userID)
		return userID
	}

	// 没有找到用户信息,用户未登录
	return ""
}

// hasUserConsented 检查用户是否已经同意授
func (h *OAuth2StandardHandler) hasUserConsented(userID, clientID, scope string) bool {
	// 这里应该查询数据库检查用户是否已经授权过此应用和权限范围
	// 简化实现,总是返回false,要求用户每次都确认
	// 在实际实现中,可以存储用户的授权记录
	return false
}

// processAuthorizeRequest 处理授权请求
func (h *OAuth2StandardHandler) processAuthorizeRequest(ctx *gin.Context, userID string, approved bool) {
	// 获取授权请求参数
	clientID := ctx.Query("client_id")
	redirectURI := ctx.Query("redirect_uri")
	responseType := ctx.Query("response_type")
	scope := ctx.Query("scope")
	state := ctx.Query("state")
	codeChallenge := ctx.Query("code_challenge")
	codeChallengeMethod := ctx.Query("code_challenge_method")
	nonce := ctx.Query("nonce")

	// 避免未使用变量的编译错误
	_ = state
	_ = codeChallenge
	_ = codeChallengeMethod

	// 转换userID为uint类型
	var userIDUint uint
	if userID != "" {
		if id, err := strconv.ParseUint(userID, 10, 32); err == nil {
			userIDUint = uint(id)
		}
	}

	// 处理授权请求
	result, err := h.oauth2Service.HandleAuthorizeRequest(ctx, &oauth2.AuthorizeRequest{
		ClientID:            clientID,
		RedirectURI:         redirectURI,
		ResponseType:        responseType,
		Scope:               scope,
		State:               state,
		CodeChallenge:       codeChallenge,
		CodeChallengeMethod: codeChallengeMethod,
		Nonce:               nonce,
		UserID:              userIDUint,
		Approved:            approved,
	})

	if err != nil {
		// 如果有重定向URI,将错误重定向到客户
		if redirectURI != "" {
			errorURL := redirectURI + "?error=server_error&error_description=" + url.QueryEscape(err.Error())
			if state != "" {
				errorURL += "&state=" + url.QueryEscape(state)
			}
			ctx.Redirect(http.StatusFound, errorURL)
		} else {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"error":             "server_error",
				"error_description": err.Error(),
			})
		}
		return
	}

	// 重定向到客户
	ctx.Redirect(http.StatusFound, result.RedirectURL)
}

// validateAuthorizationRequest 验证授权请求参数
func (h *OAuth2StandardHandler) validateAuthorizationRequest(
	clientID, redirectURI, responseType, scope, state, nonce, codeChallenge, codeChallengeMethod string,
) error {
	// 1. 验证client_id
	if clientID == "" {
		return fmt.Errorf("client_id is required")
	}

	// 2. 验证response_type
	supportedResponseTypes := []string{"code", "token", "id_token"}
	validResponseType := false
	for _, rt := range supportedResponseTypes {
		if responseType == rt {
			validResponseType = true
			break
		}
	}
	if !validResponseType {
		return fmt.Errorf("unsupported response_type: %s", responseType)
	}

	// 3. 验证redirect_uri (基础验证,详细验证在后续步骤)
	if redirectURI == "" {
		return fmt.Errorf("redirect_uri is required")
	}

	// 4. 验证state参数 (可选但推荐)
	// state参数是可选的，不需要验证

	// 5. 验证nonce参数 (OpenID Connect需要)
	if strings.Contains(scope, "openid") && nonce == "" {
		return fmt.Errorf("nonce is required for OpenID Connect requests")
	}

	// 6. 验证PKCE参数 (可选但推荐)
	if codeChallenge != "" && codeChallengeMethod == "" {
		return fmt.Errorf("code_challenge_method is required when code_challenge is provided")
	}

	return nil
}

// JWKS godoc
// @Summary JSON Web Key Set端点
// @Schemes
// @Description 返回用于验证JWT签名的公钥集合,符合RFC 7517规范
// @Tags OpenID Connect
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "JWKS"
// @Failure 500 {object} gin.H "服务器错

// @Router /oauth2/jwks [get]
func (h *OAuth2StandardHandler) JWKS(ctx *gin.Context) {
	h.logger.WithContext(ctx).Info("JWKS request",
		zap.String("user_agent", ctx.GetHeader("User-Agent")),
		zap.String("origin", ctx.GetHeader("Origin")),
		zap.String("referer", ctx.GetHeader("Referer")))

	jwks, err := h.oidcService.GetJWKS(ctx)
	if err != nil {
		h.logger.WithContext(ctx).Error("Failed to generate JWKS", zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":             "server_error",
			"error_description": "Failed to generate JWKS",
		})
		return
	}

	// 设置标准的JWKS响应
	h.setJWKSHeaders(ctx)

	// 返回JWKS
	ctx.JSON(http.StatusOK, jwks)
}

// setJWKSHeaders 设置JWKS响应的标准HTTP
func (h *OAuth2StandardHandler) setJWKSHeaders(ctx *gin.Context) {
	// 设置内容类型
	ctx.Header("Content-Type", "application/json; charset=utf-8")

	// 设置缓存控制 - 24小时缓存,但允许重新验证
	ctx.Header("Cache-Control", "public, max-age=86400, must-revalidate")

	// 设置CORS头部,允许跨域访
	ctx.Header("Access-Control-Allow-Origin", "*")
	ctx.Header("Access-Control-Allow-Methods", "GET, OPTIONS")
	ctx.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

	// 设置安全头部
	ctx.Header("X-Content-Type-Options", "nosniff")
	ctx.Header("X-Frame-Options", "DENY")
	ctx.Header("X-XSS-Protection", "1; mode=block")

	// 设置最后修改时
	ctx.Header("Last-Modified", time.Now().UTC().Format(http.TimeFormat))

	// 设置Vary头部,用于缓存控
	ctx.Header("Vary", "Accept-Encoding, Origin")
}

// Authorize godoc
// @Summary OAuth2授权端点
// @Schemes
// @Description OAuth2授权端点,处理授权码流程
// @Tags OAuth2
// @Accept json
// @Produce json
// @Param client_id query string true "客户端ID"
// @Param redirect_uri query string true "重定向URI"
// @Param response_type query string true "响应类型"
// @Param scope query string false "权限范围"
// @Param state query string false "状态参

// @Param code_challenge query string false "PKCE代码挑战"
// @Param code_challenge_method query string false "PKCE代码挑战方法"
// @Param login_hint query string false "登录提示（邮箱、用户名等）"
// @Success 302 {string} string "重定向到授权页面或回调地址"
// @Failure 400 {object} map[string]interface{} "请求参数错误"
// @Router /oauth2/authorize [get]
func (h *OAuth2StandardHandler) Authorize(ctx *gin.Context) {
	// 获取授权请求参数
	clientID := ctx.Query("client_id")
	redirectURI := ctx.Query("redirect_uri")
	responseType := ctx.Query("response_type")
	scope := ctx.Query("scope")
	state := ctx.Query("state")
	codeChallenge := ctx.Query("code_challenge")
	codeChallengeMethod := ctx.Query("code_challenge_method")
	nonce := ctx.Query("nonce")

	// 处理login_hint参数
	loginHintProcessor := oauth2.GetDefaultLoginHintProcessor()
	loginHint := loginHintProcessor.ProcessLoginHint(ctx)

	// 全面的参数验
	if err := h.validateAuthorizationRequest(clientID, redirectURI, responseType, scope, state, nonce, codeChallenge, codeChallengeMethod); err != nil {
		if oauth2Err, ok := err.(*oauth2.OAuth2Error); ok {
			h.respondWithOAuth2Error(ctx, oauth2Err)
		} else {
			h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
				Code:        "invalid_request",
				Description: err.Error(),
			})
		}
		return
	}

	// 检查用户是否已登录
	userID := h.getCurrentUserID(ctx)
	if userID == "" {
		// 用户未登录,重定向到登录页面
		// 如果有login_hint,将其包含在登录URL
		baseLoginURL := "/login"
		redirectURI := ctx.Request.URL.String()
		loginURL := oauth2.BuildLoginURLWithHint(baseLoginURL, loginHint, redirectURI)

		ctx.Redirect(http.StatusFound, loginURL)
		return
	}

	// 检查用户是否已经授权过此应用和权限范围
	if h.hasUserConsented(userID, clientID, scope) {
		// 用户已授权,直接处理授权请求
		h.processAuthorizeRequest(ctx, userID, true)
		return
	}

	// 用户未授权,重定向到同意页面
	consentURL := fmt.Sprintf("/oauth2/consent?%s", ctx.Request.URL.RawQuery)
	ctx.Redirect(http.StatusFound, consentURL)
}

// Token godoc
// @Summary OAuth2令牌端点
// @Schemes
// @Description OAuth2令牌端点,处理令牌交
// @Tags OAuth2
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param grant_type formData string true "授权类型"
// @Param code formData string false "授权

// @Param redirect_uri formData string false "重定向URI"
// @Param client_id formData string false "客户端ID"
// @Param client_secret formData string false "客户端密

// @Param code_verifier formData string false "PKCE代码验证

// @Success 200 {object} map[string]interface{} "令牌响应"
// @Failure 400 {object} map[string]interface{} "请求参数错误"
// @Failure 401 {object} map[string]interface{} "客户端认证失

// @Router /oauth2/token [post]
func (h *OAuth2StandardHandler) Token(ctx *gin.Context) {
	// 动态选择处理方式:如果TokenHandler已启用,使用完整实现;否则使用增强的传统实现
	h.SwitchToFullTokenHandler(ctx)
}

// handleTokenRequestWithEnhancedHandler 使用新的TokenHandler处理令牌请求
func (h *OAuth2StandardHandler) handleTokenRequestWithEnhancedHandler(ctx *gin.Context) {
	// 检查grant_type参数
	grantType := ctx.PostForm("grant_type")
	if grantType == "" {
		h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
			Code:        "invalid_request",
			Description: "Missing grant_type parameter",
		})
		return
	}

	// 使用已经增强的grant处理方法,这些方法已经集成了新的错误处理
	switch grantType {
	case oauth2.GrantTypeAuthorizationCode:
		h.handleAuthorizationCodeGrant(ctx)
	case oauth2.GrantTypeRefreshToken:
		h.handleRefreshTokenGrant(ctx)
	case oauth2.GrantTypeClientCredentials:
		h.handleClientCredentialsGrant(ctx)
	default:
		h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
			Code:        "unsupported_grant_type",
			Description: "The grant type is not supported",
		})
	}
}

// handleTokenRequestWithFullTokenHandler 使用完整TokenHandler的令牌请求处理
// 这是未来完全替换现有实现的方法,当所有依赖都实现后启用
func (h *OAuth2StandardHandler) handleTokenRequestWithFullTokenHandler(ctx *gin.Context) {
	// 使用TokenHandler解析和验证参数
	params, err := h.tokenHandler.ParseTokenParams(ctx)
	if err != nil {
		if oauthErr, ok := err.(*oauth2.OAuth2Error); ok {
			h.respondWithOAuth2Error(ctx, oauthErr)
		} else {
			h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
				Code:        "server_error",
				Description: "Internal server error during parameter parsing",
			})
		}
		return
	}

	// 使用TokenHandler创建令牌响应
	tokenResponse, err := h.tokenHandler.CreateTokenResponse(ctx.Request.Context(), params)
	if err != nil {
		if oauthErr, ok := err.(*oauth2.OAuth2Error); ok {
			h.respondWithOAuth2Error(ctx, oauthErr)
		} else {
			h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
				Code:        "server_error",
				Description: "Failed to create token response",
			})
		}
		return
	}

	// 设置标准OAuth2响应头并返回令牌
	h.setOAuth2ResponseHeaders(ctx)
	ctx.JSON(http.StatusOK, tokenResponse)
}

// EnableFullTokenHandler 启用完整的TokenHandler功能
// 当repository和JWT manager都准备好时调用此方法
func (h *OAuth2StandardHandler) EnableFullTokenHandler(tokenRepo oauth2.TokenRepository, jwtManager *oauth2.JWTManager) {
	h.tokenHandler = oauth2.NewTokenHandler(tokenRepo, jwtManager)
}

// IsTokenHandlerEnabled 检查TokenHandler是否已启用
func (h *OAuth2StandardHandler) IsTokenHandlerEnabled() bool {
	return h.tokenHandler != nil
}

// SwitchToFullTokenHandler 切换到完整的TokenHandler处理
// 这个方法可以在运行时动态切换处理方式
func (h *OAuth2StandardHandler) SwitchToFullTokenHandler(ctx *gin.Context) {
	if !h.IsTokenHandlerEnabled() {
		// 如果TokenHandler未启用,回退到增强的传统处理方式
		h.handleTokenRequestWithEnhancedHandler(ctx)
		return
	}

	// 使用完整的TokenHandler处理
	h.handleTokenRequestWithFullTokenHandler(ctx)
}

// respondWithOAuth2Error 响应OAuth2标准错误 - 符合RFC 6749
func (h *OAuth2StandardHandler) respondWithOAuth2Error(ctx *gin.Context, err *oauth2.OAuth2Error) {
	// 设置标准OAuth2响应头
	h.setOAuth2ResponseHeaders(ctx)

	// 转换为标准错误响应格式
	errorResponse := err.ToErrorResponse()

	// 使用OAuth2Error的HTTPStatus方法获取正确的状态码
	ctx.JSON(err.GetHTTPStatus(), errorResponse)
}

// setOAuth2ResponseHeaders 设置OAuth2标准响应头 - 符合RFC 6749和安全最佳实践
func (h *OAuth2StandardHandler) setOAuth2ResponseHeaders(ctx *gin.Context) {
	// RFC 6749要求的Content-Type
	ctx.Header("Content-Type", "application/json")

	// RFC 6749推荐的缓存控制头
	ctx.Header("Cache-Control", "no-store")
	ctx.Header("Pragma", "no-cache")

	// 安全最佳实践头
	ctx.Header("X-Content-Type-Options", "nosniff")
	ctx.Header("X-Frame-Options", "DENY")
	ctx.Header("X-XSS-Protection", "1; mode=block")
	ctx.Header("Referrer-Policy", "strict-origin-when-cross-origin")

	// CORS头 - 生产环境应该更严格
	// TODO: 在生产环境中应该配置具体的允许源，而不是使用通配符
	ctx.Header("Access-Control-Allow-Origin", "*")
	ctx.Header("Access-Control-Allow-Methods", "POST, OPTIONS")
	ctx.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
}

// handleAuthorizationCodeGrant 处理授权码授权,增强版本参考authentik实现
func (h *OAuth2StandardHandler) handleAuthorizationCodeGrant(ctx *gin.Context) {
	code := ctx.PostForm("code")
	redirectURI := ctx.PostForm("redirect_uri")
	codeVerifier := ctx.PostForm("code_verifier")

	// 提取客户端认证信息,使用增强的错误处理
	clientCreds, err := oauth2.ExtractClientAuth(ctx)
	if err != nil {
		if oauthErr, ok := err.(*oauth2.OAuth2Error); ok {
			h.respondWithOAuth2Error(ctx, oauthErr)
		} else {
			h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
				Code:        "invalid_client",
				Description: "Client authentication failed",
			})
		}
		return
	}

	// 参数验证,参考authentik的严格验证
	if code == "" {
		h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
			Code:        "invalid_grant",
			Description: "Missing authorization code",
		})
		return
	}

	if redirectURI == "" {
		h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
			Code:        "invalid_request",
			Description: "Missing redirect_uri parameter",
		})
		return
	}

	// 处理令牌交换
	tokenResponse, err := h.oauth2Service.ExchangeAuthorizationCode(ctx, &oauth2.TokenRequest{
		GrantType:    oauth2.GrantTypeAuthorizationCode,
		Code:         code,
		RedirectURI:  redirectURI,
		ClientID:     clientCreds.ClientID,
		ClientSecret: clientCreds.ClientSecret,
		CodeVerifier: codeVerifier,
	})

	if err != nil {
		// 🔧 P1-1修复：正确处理OAuth2错误类型，避免错误嵌套
		if oauth2Err, ok := err.(*oauth2.OAuth2Error); ok {
			// 如果是OAuth2Error，直接使用原始错误
			h.respondWithOAuth2Error(ctx, oauth2Err)
		} else {
			// 如果是其他错误，包装为invalid_grant
			h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
				Code:        "invalid_grant",
				Description: err.Error(),
			})
		}
		return
	}

	// 设置标准OAuth2响应头并返回令牌
	h.setOAuth2ResponseHeaders(ctx)
	ctx.JSON(http.StatusOK, tokenResponse)
}

// handleRefreshTokenGrant 处理刷新令牌授权,增强版本参考authentik实现
func (h *OAuth2StandardHandler) handleRefreshTokenGrant(ctx *gin.Context) {
	refreshToken := ctx.PostForm("refresh_token")
	scope := ctx.PostForm("scope")

	// 提取客户端认证信息,使用增强的错误处理
	clientCreds, err := oauth2.ExtractClientAuth(ctx)
	if err != nil {
		if oauthErr, ok := err.(*oauth2.OAuth2Error); ok {
			h.respondWithOAuth2Error(ctx, oauthErr)
		} else {
			h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
				Code:        "invalid_client",
				Description: "Client authentication failed",
			})
		}
		return
	}

	// 参数验证,参考authentik的严格验证
	if refreshToken == "" {
		h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
			Code:        "invalid_grant",
			Description: "Missing refresh token",
		})
		return
	}

	// 处理令牌刷新
	tokenResponse, err := h.oauth2Service.RefreshToken(ctx, &oauth2.RefreshTokenRequest{
		RefreshToken: refreshToken,
		ClientID:     clientCreds.ClientID,
		ClientSecret: clientCreds.ClientSecret,
		Scope:        scope,
	})

	if err != nil {
		// 🔧 P1-1修复：正确处理OAuth2错误类型，避免错误嵌套
		if oauth2Err, ok := err.(*oauth2.OAuth2Error); ok {
			// 如果是OAuth2Error，直接使用原始错误
			h.respondWithOAuth2Error(ctx, oauth2Err)
		} else {
			// 如果是其他错误，包装为invalid_grant
			h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
				Code:        "invalid_grant",
				Description: err.Error(),
			})
		}
		return
	}

	// 设置标准OAuth2响应头并返回令牌
	h.setOAuth2ResponseHeaders(ctx)
	ctx.JSON(http.StatusOK, tokenResponse)
}

// handleClientCredentialsGrant 处理客户端凭证授权,增强版本参考authentik实现
func (h *OAuth2StandardHandler) handleClientCredentialsGrant(ctx *gin.Context) {
	scope := ctx.PostForm("scope")

	// 提取客户端认证信息,使用增强的错误处理
	clientCreds, err := oauth2.ExtractClientAuth(ctx)
	if err != nil {
		if oauthErr, ok := err.(*oauth2.OAuth2Error); ok {
			h.respondWithOAuth2Error(ctx, oauthErr)
		} else {
			h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
				Code:        "invalid_client",
				Description: "Client authentication failed",
			})
		}
		return
	}

	// 支持JWT客户端断言,参考authentik的client assertion处理
	clientAssertion := ctx.PostForm("client_assertion")
	clientAssertionType := ctx.PostForm("client_assertion_type")

	// 处理客户端凭证授权
	tokenResponse, err := h.oauth2Service.ClientCredentialsGrant(ctx, &oauth2.ClientCredentialsRequest{
		GrantType:    oauth2.GrantTypeClientCredentials,
		ClientID:     clientCreds.ClientID,
		ClientSecret: clientCreds.ClientSecret,
		Scope:        scope,
	})

	if err != nil {
		// 🔧 P1-1修复：正确处理OAuth2错误类型，避免错误嵌套
		if oauth2Err, ok := err.(*oauth2.OAuth2Error); ok {
			// 如果是OAuth2Error，直接使用原始错误
			h.respondWithOAuth2Error(ctx, oauth2Err)
		} else {
			// 如果是其他错误，包装为server_error
			h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
				Code:        "server_error",
				Description: err.Error(),
			})
		}
		return
	}

	// 记录客户端断言参数（用于未来扩展）
	if clientAssertion != "" && clientAssertionType == oauth2.ClientAssertionTypeJWT {
		// 这里可以添加JWT客户端断言的处理逻辑
		// 参考authentik的__post_init_client_credentials_jwt方法
	}

	// 设置标准OAuth2响应头并返回令牌
	h.setOAuth2ResponseHeaders(ctx)
	ctx.JSON(http.StatusOK, tokenResponse)
}

// Revoke godoc
// @Summary OAuth2 Token Revocation端点
// @Description 撤销访问令牌或刷新令牌,符合RFC 7009规范
// @Tags OAuth2/OIDC
// @Accept application/x-www-form-urlencoded
// @Param token formData string true "要撤销的令牌"
// @Param token_type_hint formData string false "令牌类型提示 (access_token 或 refresh_token)"
// @Param client_id formData string false "客户端ID"
// @Param client_secret formData string false "客户端密钥"
// @Success 200 {string} string "撤销成功"
// @Failure 400 {object} oauth2.OAuth2Error "参数错误"
// @Failure 401 {object} oauth2.OAuth2Error "客户端认证失败"
// @Failure 500 {object} oauth2.OAuth2Error "服务器错误"
// @Router /oauth2/revoke [post]
func (h *OAuth2StandardHandler) Revoke(ctx *gin.Context) {
	// 设置标准OAuth2响应头
	h.setOAuth2ResponseHeaders(ctx)

	// 解析请求参数
	token := ctx.PostForm("token")
	tokenTypeHint := ctx.PostForm("token_type_hint")
	clientID := ctx.PostForm("client_id")
	_ = ctx.PostForm("client_secret") // 客户端密钥（暂未使用）

	if token == "" {
		h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
			Code:        "invalid_request",
			Description: "Missing required parameter: token",
		})
		return
	}

	// 客户端认证（如果提供了客户端凭据）
	if clientID != "" {
		// TODO: 实现客户端认证逻辑
		h.logger.Debug("Client authentication for revoke",
			zap.String("client_id", clientID))
	}

	// 执行令牌撤销
	// TODO: 实现实际的令牌撤销逻辑
	h.logger.Info("Token revocation request",
		zap.String("token_type_hint", tokenTypeHint),
		zap.String("client_id", clientID))

	// 根据RFC 7009,成功的撤销请求应该返回HTTP 200
	ctx.Status(http.StatusOK)
}

// Introspect godoc
// @Summary OAuth2 Token Introspection端点
// @Description 检查令牌的状态和元数据,符合RFC 7662规范
// @Tags OAuth2/OIDC
// @Accept application/x-www-form-urlencoded
// @Param token formData string true "要检查的令牌"
// @Param token_type_hint formData string false "令牌类型提示 (access_token 或 refresh_token)"
// @Param client_id formData string false "客户端ID"
// @Param client_secret formData string false "客户端密钥"
// @Success 200 {object} map[string]interface{} "令牌信息"
// @Failure 400 {object} oauth2.OAuth2Error "参数错误"
// @Failure 401 {object} oauth2.OAuth2Error "客户端认证失败"
// @Failure 500 {object} oauth2.OAuth2Error "服务器错误"
// @Router /oauth2/introspect [post]
func (h *OAuth2StandardHandler) Introspect(ctx *gin.Context) {
	// 设置标准OAuth2响应头
	h.setOAuth2ResponseHeaders(ctx)

	// 解析请求参数
	token := ctx.PostForm("token")
	tokenTypeHint := ctx.PostForm("token_type_hint")
	clientID := ctx.PostForm("client_id")
	_ = ctx.PostForm("client_secret") // 客户端密钥（暂未使用）

	if token == "" {
		h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
			Code:        "invalid_request",
			Description: "Missing required parameter: token",
		})
		return
	}

	// 客户端认证
	if clientID == "" {
		h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
			Code:        "invalid_client",
			Description: "Client authentication required",
		})
		return
	}

	// TODO: 实现客户端认证逻辑
	h.logger.Debug("Client authentication for introspect",
		zap.String("client_id", clientID))

	// 执行令牌内省
	// TODO: 实现实际的令牌内省逻辑
	h.logger.Info("Token introspection request",
		zap.String("token_type_hint", tokenTypeHint),
		zap.String("client_id", clientID))

	// 返回内省结果（当前返回不活跃状态）
	response := map[string]interface{}{
		"active": false,
	}

	ctx.JSON(http.StatusOK, response)
}

// EndSession godoc
// @Summary OpenID Connect End Session端点
// @Description 处理用户登出请求,支持id_token_hint和post_logout_redirect_uri参数
// @Tags OAuth2/OIDC
// @Accept application/x-www-form-urlencoded
// @Param id_token_hint query string false "ID Token提示"
// @Param post_logout_redirect_uri query string false "登出后重定向URI"
// @Param state query string false "状态参数"
// @Success 302 {string} string "重定向到登出后页面"
// @Failure 400 {object} oauth2.OAuth2Error "参数错误"
// @Failure 500 {object} oauth2.OAuth2Error "服务器错误"
// @Router /oauth2/end-session [get,post]
func (h *OAuth2StandardHandler) EndSession(ctx *gin.Context) {
	// 设置安全响应头
	ctx.Header("Cache-Control", "no-store")
	ctx.Header("Pragma", "no-cache")

	// 解析请求参数
	idTokenHint := ctx.Query("id_token_hint")
	postLogoutRedirectURI := ctx.Query("post_logout_redirect_uri")
	state := ctx.Query("state")

	// 如果是POST请求,也从表单中获取参数
	if ctx.Request.Method == http.MethodPost {
		if idTokenHint == "" {
			idTokenHint = ctx.PostForm("id_token_hint")
		}
		if postLogoutRedirectURI == "" {
			postLogoutRedirectURI = ctx.PostForm("post_logout_redirect_uri")
		}
		if state == "" {
			state = ctx.PostForm("state")
		}
	}

	h.logger.Info("End session request received",
		zap.String("post_logout_redirect_uri", postLogoutRedirectURI),
		zap.String("state", state))

	// TODO: 实现实际的登出逻辑
	// 1. 验证ID Token Hint
	// 2. 清除用户会话
	// 3. 验证重定向URI
	// 4. 执行重定向

	// 构建重定向URL
	if postLogoutRedirectURI != "" {
		redirectURL := postLogoutRedirectURI
		if state != "" {
			// 添加state参数
			if strings.Contains(redirectURL, "?") {
				redirectURL += "&state=" + url.QueryEscape(state)
			} else {
				redirectURL += "?state=" + url.QueryEscape(state)
			}
		}
		ctx.Redirect(http.StatusFound, redirectURL)
		return
	}

	// 默认重定向到登录页面
	ctx.Redirect(http.StatusFound, "/login?logout=success")
}

// DeviceAuthorization 设备授权端点 (RFC 8628)
func (h *OAuth2StandardHandler) DeviceAuthorization(ctx *gin.Context) {
	// 解析请求参数
	var req oauth2.DeviceAuthorizationRequest
	if err := ctx.ShouldBind(&req); err != nil {
		h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
			Code:        oauth2.ErrorInvalidRequest,
			Description: "Invalid request parameters",
		})
		return
	}

	// 验证客户端
	if req.ClientID == "" {
		h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
			Code:        oauth2.ErrorInvalidRequest,
			Description: "Missing client_id parameter",
		})
		return
	}

	// 验证客户端是否存在 - 暂时跳过详细验证，专注于设备流程实现
	// TODO: 实现完整的客户端验证逻辑

	// 基本的客户端ID验证
	if len(req.ClientID) < 3 {
		h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
			Code:        oauth2.ErrorInvalidClient,
			Description: "Invalid client_id format",
		})
		return
	}

	// 创建设备流程管理器
	deviceManager := oauth2.NewDeviceFlowManager("http://localhost:8080") // TODO: 从配置获取

	// 创建设备授权
	response, deviceData, err := deviceManager.CreateDeviceAuthorization(&req)
	if err != nil {
		h.respondWithOAuth2Error(ctx, &oauth2.OAuth2Error{
			Code:        oauth2.ErrorServerError,
			Description: "Failed to create device authorization",
		})
		return
	}

	// TODO: 将deviceData保存到数据库
	_ = deviceData

	// 设置响应头并返回
	h.setOAuth2ResponseHeaders(ctx)
	ctx.JSON(http.StatusOK, response)
}

// DeviceVerification 设备验证页面 - 用户输入用户代码的页面
func (h *OAuth2StandardHandler) DeviceVerification(ctx *gin.Context) {
	userCode := ctx.Query("user_code")

	if ctx.Request.Method == "GET" {
		// 显示设备验证页面
		ctx.HTML(http.StatusOK, "device_verification.html", gin.H{
			"user_code": userCode,
			"title":     "Device Verification",
		})
		return
	}

	// POST请求 - 处理用户代码验证
	var req oauth2.DeviceUserVerification
	if err := ctx.ShouldBind(&req); err != nil {
		ctx.HTML(http.StatusBadRequest, "device_verification.html", gin.H{
			"error":     "Invalid request",
			"user_code": userCode,
		})
		return
	}

	// 验证用户代码格式
	deviceManager := oauth2.NewDeviceFlowManager("http://localhost:8080")
	if err := deviceManager.ValidateUserCode(req.UserCode); err != nil {
		ctx.HTML(http.StatusBadRequest, "device_verification.html", gin.H{
			"error":     "Invalid user code format",
			"user_code": req.UserCode,
		})
		return
	}

	// TODO: 验证用户代码并处理授权
	// 这里需要：
	// 1. 检查用户是否已登录
	// 2. 验证用户代码是否存在且有效
	// 3. 显示授权确认页面
	// 4. 处理用户授权决定

	ctx.HTML(http.StatusOK, "device_authorization.html", gin.H{
		"user_code": deviceManager.FormatUserCode(req.UserCode),
		"title":     "Authorize Device",
	})
}
