package mappings

import (
	"fmt"
	"time"

	"github.com/google/uuid"
)

// PropertyMappingType 属性映射类型
type PropertyMappingType string

const (
	PropertyMappingTypeScope      PropertyMappingType = "scope"
	PropertyMappingTypeSAML       PropertyMappingType = "saml"
	PropertyMappingTypeLDAP       PropertyMappingType = "ldap"
	PropertyMappingTypeOAuth2     PropertyMappingType = "oauth2"
	PropertyMappingTypeNotification PropertyMappingType = "notification"
)

// PropertyMapping 属性映射接口
type PropertyMapping interface {
	// GetUUID 返回映射的UUID
	GetUUID() uuid.UUID
	
	// GetName 返回映射的名称
	GetName() string
	
	// GetMappingType 返回映射类型
	GetMappingType() PropertyMappingType
	
	// Evaluate 评估映射表达式
	Evaluate(context map[string]interface{}) (interface{}, error)
	
	// Validate 验证映射配置
	Validate() error
}

// BasePropertyMapping 属性映射基础实现
type BasePropertyMapping struct {
	MappingUUID uuid.UUID `json:"mapping_uuid" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	Name        string    `json:"name" gorm:"uniqueIndex;not null"`
	Expression  string    `json:"expression" gorm:"type:text;not null"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

func (m *BasePropertyMapping) GetUUID() uuid.UUID {
	return m.MappingUUID
}

func (m *BasePropertyMapping) GetName() string {
	return m.Name
}

// ScopeMapping OAuth2 Scope映射
type ScopeMapping struct {
	BasePropertyMapping
	
	// Scope信息
	ScopeName   string `json:"scope_name" gorm:"not null"`
	Description string `json:"description"`
}

func (m *ScopeMapping) GetMappingType() PropertyMappingType {
	return PropertyMappingTypeScope
}

func (m *ScopeMapping) Evaluate(context map[string]interface{}) (interface{}, error) {
	// 简化的表达式评估
	// 在实际实现中，这里应该使用表达式引擎
	
	result := make(map[string]interface{})
	
	// 根据表达式和上下文生成属性
	switch m.Expression {
	case "return user.username":
		if user, exists := context["user"]; exists {
			if userMap, ok := user.(map[string]interface{}); ok {
				if username, exists := userMap["username"]; exists {
					result["username"] = username
				}
			}
		}
	case "return user.email":
		if user, exists := context["user"]; exists {
			if userMap, ok := user.(map[string]interface{}); ok {
				if email, exists := userMap["email"]; exists {
					result["email"] = email
				}
			}
		}
	case "return user.groups":
		if user, exists := context["user"]; exists {
			if userMap, ok := user.(map[string]interface{}); ok {
				if groups, exists := userMap["groups"]; exists {
					result["groups"] = groups
				}
			}
		}
	default:
		// 默认返回用户基本信息
		if user, exists := context["user"]; exists {
			result["user"] = user
		}
	}
	
	return result, nil
}

func (m *ScopeMapping) Validate() error {
	if m.Name == "" {
		return fmt.Errorf("mapping name cannot be empty")
	}
	if m.ScopeName == "" {
		return fmt.Errorf("scope name cannot be empty")
	}
	if m.Expression == "" {
		return fmt.Errorf("expression cannot be empty")
	}
	return nil
}

// SAMLPropertyMapping SAML属性映射
type SAMLPropertyMapping struct {
	BasePropertyMapping
	
	// SAML属性信息
	SAMLName       string `json:"saml_name" gorm:"not null"`
	FriendlyName   string `json:"friendly_name"`
	NameFormat     string `json:"name_format" gorm:"default:'urn:oasis:names:tc:SAML:2.0:attrname-format:basic'"`
}

func (m *SAMLPropertyMapping) GetMappingType() PropertyMappingType {
	return PropertyMappingTypeSAML
}

func (m *SAMLPropertyMapping) Evaluate(context map[string]interface{}) (interface{}, error) {
	// 简化的SAML属性映射
	result := make(map[string]interface{})
	
	// 根据表达式生成SAML属性
	switch m.Expression {
	case "return user.username":
		if user, exists := context["user"]; exists {
			if userMap, ok := user.(map[string]interface{}); ok {
				if username, exists := userMap["username"]; exists {
					result[m.SAMLName] = username
				}
			}
		}
	case "return user.email":
		if user, exists := context["user"]; exists {
			if userMap, ok := user.(map[string]interface{}); ok {
				if email, exists := userMap["email"]; exists {
					result[m.SAMLName] = email
				}
			}
		}
	case "return user.first_name + ' ' + user.last_name":
		if user, exists := context["user"]; exists {
			if userMap, ok := user.(map[string]interface{}); ok {
				firstName, _ := userMap["first_name"].(string)
				lastName, _ := userMap["last_name"].(string)
				if firstName != "" || lastName != "" {
					result[m.SAMLName] = firstName + " " + lastName
				}
			}
		}
	default:
		// 默认映射
		if user, exists := context["user"]; exists {
			result[m.SAMLName] = user
		}
	}
	
	return result, nil
}

func (m *SAMLPropertyMapping) Validate() error {
	if m.Name == "" {
		return fmt.Errorf("mapping name cannot be empty")
	}
	if m.SAMLName == "" {
		return fmt.Errorf("SAML name cannot be empty")
	}
	if m.Expression == "" {
		return fmt.Errorf("expression cannot be empty")
	}
	return nil
}

// LDAPPropertyMapping LDAP属性映射
type LDAPPropertyMapping struct {
	BasePropertyMapping
	
	// LDAP属性信息
	ObjectField string `json:"object_field" gorm:"not null"`
}

func (m *LDAPPropertyMapping) GetMappingType() PropertyMappingType {
	return PropertyMappingTypeLDAP
}

func (m *LDAPPropertyMapping) Evaluate(context map[string]interface{}) (interface{}, error) {
	// 简化的LDAP属性映射
	result := make(map[string]interface{})
	
	// 根据表达式映射LDAP属性到用户属性
	switch m.Expression {
	case "return ldap.cn":
		if ldap, exists := context["ldap"]; exists {
			if ldapMap, ok := ldap.(map[string]interface{}); ok {
				if cn, exists := ldapMap["cn"]; exists {
					result[m.ObjectField] = cn
				}
			}
		}
	case "return ldap.mail":
		if ldap, exists := context["ldap"]; exists {
			if ldapMap, ok := ldap.(map[string]interface{}); ok {
				if mail, exists := ldapMap["mail"]; exists {
					result[m.ObjectField] = mail
				}
			}
		}
	case "return ldap.memberOf":
		if ldap, exists := context["ldap"]; exists {
			if ldapMap, ok := ldap.(map[string]interface{}); ok {
				if memberOf, exists := ldapMap["memberOf"]; exists {
					result[m.ObjectField] = memberOf
				}
			}
		}
	default:
		// 默认映射
		if ldap, exists := context["ldap"]; exists {
			result[m.ObjectField] = ldap
		}
	}
	
	return result, nil
}

func (m *LDAPPropertyMapping) Validate() error {
	if m.Name == "" {
		return fmt.Errorf("mapping name cannot be empty")
	}
	if m.ObjectField == "" {
		return fmt.Errorf("object field cannot be empty")
	}
	if m.Expression == "" {
		return fmt.Errorf("expression cannot be empty")
	}
	return nil
}

// NotificationPropertyMapping 通知属性映射
type NotificationPropertyMapping struct {
	BasePropertyMapping
}

func (m *NotificationPropertyMapping) GetMappingType() PropertyMappingType {
	return PropertyMappingTypeNotification
}

func (m *NotificationPropertyMapping) Evaluate(context map[string]interface{}) (interface{}, error) {
	// 简化的通知属性映射
	result := make(map[string]interface{})
	
	// 根据表达式生成通知内容
	switch m.Expression {
	case "return f'Hello {user.username}'":
		if user, exists := context["user"]; exists {
			if userMap, ok := user.(map[string]interface{}); ok {
				if username, exists := userMap["username"]; exists {
					result["message"] = fmt.Sprintf("Hello %s", username)
				}
			}
		}
	case "return f'Welcome to {application.name}'":
		if app, exists := context["application"]; exists {
			if appMap, ok := app.(map[string]interface{}); ok {
				if name, exists := appMap["name"]; exists {
					result["message"] = fmt.Sprintf("Welcome to %s", name)
				}
			}
		}
	default:
		result["message"] = "Default notification message"
	}
	
	return result, nil
}

func (m *NotificationPropertyMapping) Validate() error {
	if m.Name == "" {
		return fmt.Errorf("mapping name cannot be empty")
	}
	if m.Expression == "" {
		return fmt.Errorf("expression cannot be empty")
	}
	return nil
}

// PropertyMappingUsage 属性映射使用记录
type PropertyMappingUsage struct {
	UsageUUID uuid.UUID `json:"usage_uuid" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	
	// 关联的映射
	MappingUUID uuid.UUID `json:"mapping_uuid" gorm:"not null"`
	
	// 使用对象
	ObjectType string    `json:"object_type" gorm:"not null"` // "source", "provider", "application"
	ObjectUUID uuid.UUID `json:"object_uuid" gorm:"not null"`
	
	// 使用统计
	UsageCount int       `json:"usage_count" gorm:"default:0"`
	LastUsed   time.Time `json:"last_used"`
	
	// 时间戳
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// PropertyMappingTest 属性映射测试
type PropertyMappingTest struct {
	TestUUID uuid.UUID `json:"test_uuid" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	
	// 关联的映射
	MappingUUID uuid.UUID `json:"mapping_uuid" gorm:"not null"`
	
	// 测试输入
	TestInput map[string]interface{} `json:"test_input" gorm:"type:jsonb"`
	
	// 测试输出
	TestOutput map[string]interface{} `json:"test_output" gorm:"type:jsonb"`
	
	// 测试结果
	Success bool   `json:"success"`
	Error   string `json:"error"`
	
	// 时间戳
	CreatedAt time.Time `json:"created_at"`
}

// MappingContext 映射上下文
type MappingContext struct {
	// 用户信息
	User map[string]interface{} `json:"user,omitempty"`
	
	// 应用信息
	Application map[string]interface{} `json:"application,omitempty"`
	
	// 请求信息
	Request map[string]interface{} `json:"request,omitempty"`
	
	// LDAP信息
	LDAP map[string]interface{} `json:"ldap,omitempty"`
	
	// OAuth2信息
	OAuth2 map[string]interface{} `json:"oauth2,omitempty"`
	
	// SAML信息
	SAML map[string]interface{} `json:"saml,omitempty"`
	
	// 自定义属性
	Custom map[string]interface{} `json:"custom,omitempty"`
}
