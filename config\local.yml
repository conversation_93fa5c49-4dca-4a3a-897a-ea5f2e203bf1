env: local
http:
  #  host: 0.0.0.0
  host: 127.0.0.1
  port: 8000
security:
  api_sign:
    app_key: 123456
    app_security: 123456
  jwt:
    key: QQYnRFerJTSEcrfB89fw8prOaObmrch8
data:
  db:
    # user:
    #   driver: sqlite
    #   dsn: storage/nunu-test.db?_busy_timeout=5000
    user:
      driver: mysql
      dsn: root:root@tcp(127.0.0.1:3306)/idp?charset=utf8mb4&parseTime=True&loc=Local
  #    user:
  #      driver: postgres
  #      dsn: host=localhost user=gorm password=gorm dbname=gorm port=9920 sslmode=disable TimeZone=Asia/Shanghai
  redis:
    addr: 127.0.0.1:6350
    password: ""
    db: 0
    read_timeout: 0.2s
    write_timeout: 0.2s

log:
  log_level: debug
  encoding: console           # json or console
  log_file_name: "./storage/logs/server.log"
  max_backups: 30
  max_age: 7
  max_size: 1024
  compress: true

server:
  base_url: https://openid.akapril.in
  mode: production  # 🔧 强制使用HTTPS issuer
  host: openid.akapril.in
  port: 443

# JWT配置 - 固定RSA密钥确保签名一致性
jwt:
  issuer: https://openid.akapril.in
  key_id: default
  rsa_private_key: |
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# SSF配置 - Apple Business Manager联邦认证支持
ssf:
  enabled: true
  base_url: https://openid.akapril.in
  event_retention: 720h
  signing_keys:
    default_key_id: default
    algorithm: RS256
    key_rotation_interval: 8760h
  events:
    batch_size: 100
    retry_interval: 5m
    max_retries: 3
  delivery:
    timeout: 30s
    max_concurrent: 5