-- Create complete flows system with all related components
-- Migration: 20250805_create_complete_flows_system

-- Enable UUID extension if not exists
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ========================================
-- SOURCES TABLES
-- ========================================

-- Create sources table (base table for all source types)
CREATE TABLE IF NOT EXISTS sources (
    source_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    source_type VARCHAR(50) NOT NULL, -- 用于多态
    name VARCHAR(255) UNIQUE NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    enrollment_flow_uuid UUID REFERENCES flows(flow_uuid) ON DELETE SET NULL,
    authentication_flow_uuid UUID REFERENCES flows(flow_uuid) ON DELETE SET NULL,
    user_matching_mode VARCHAR(50) DEFAULT 'identifier',
    user_path_template VARCHAR(255) DEFAULT 'goauthentik.io/sources/%(slug)s',
    policy_engine_mode VARCHAR(20) DEFAULT 'any',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create oauth2_sources table
CREATE TABLE IF NOT EXISTS oauth2_sources (
    source_uuid UUID PRIMARY KEY REFERENCES sources(source_uuid) ON DELETE CASCADE,
    provider_type VARCHAR(50) DEFAULT 'generic',
    request_token_url VARCHAR(2048),
    authorization_url VARCHAR(2048) NOT NULL,
    access_token_url VARCHAR(2048) NOT NULL,
    profile_url VARCHAR(2048) NOT NULL,
    consumer_key VARCHAR(255) NOT NULL,
    consumer_secret VARCHAR(255) NOT NULL,
    callback_url VARCHAR(2048),
    additional_scopes TEXT
);

-- Create ldap_sources table
CREATE TABLE IF NOT EXISTS ldap_sources (
    source_uuid UUID PRIMARY KEY REFERENCES sources(source_uuid) ON DELETE CASCADE,
    server_uri VARCHAR(2048) NOT NULL,
    bind_cn VARCHAR(255),
    bind_password VARCHAR(255),
    start_tls BOOLEAN DEFAULT FALSE,
    sni_name VARCHAR(255),
    base_dn VARCHAR(255) NOT NULL,
    additional_user_dn VARCHAR(255),
    additional_group_dn VARCHAR(255),
    user_object_filter VARCHAR(255) DEFAULT '(objectClass=person)',
    group_object_filter VARCHAR(255) DEFAULT '(objectClass=group)',
    group_membership_field VARCHAR(100) DEFAULT 'member',
    object_uniqueness_field VARCHAR(100) DEFAULT 'objectSid',
    property_mappings JSONB
);

-- Create local_sources table
CREATE TABLE IF NOT EXISTS local_sources (
    source_uuid UUID PRIMARY KEY REFERENCES sources(source_uuid) ON DELETE CASCADE,
    allow_registration BOOLEAN DEFAULT TRUE,
    password_policy VARCHAR(255)
);

-- Create user_source_connections table
CREATE TABLE IF NOT EXISTS user_source_connections (
    connection_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_uuid UUID NOT NULL,
    source_uuid UUID NOT NULL REFERENCES sources(source_uuid) ON DELETE CASCADE,
    identifier VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(source_uuid, identifier)
);

-- ========================================
-- PROVIDERS TABLES
-- ========================================

-- Create providers table (base table for all provider types)
CREATE TABLE IF NOT EXISTS providers (
    provider_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    provider_type VARCHAR(50) NOT NULL, -- 用于多态
    name VARCHAR(255) UNIQUE NOT NULL,
    authentication_flow_uuid UUID REFERENCES flows(flow_uuid) ON DELETE SET NULL,
    authorization_flow_uuid UUID REFERENCES flows(flow_uuid) ON DELETE SET NULL,
    policy_engine_mode VARCHAR(20) DEFAULT 'any',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create oauth2_providers table
CREATE TABLE IF NOT EXISTS oauth2_providers (
    provider_uuid UUID PRIMARY KEY REFERENCES providers(provider_uuid) ON DELETE CASCADE,
    client_type VARCHAR(50) DEFAULT 'confidential',
    client_id VARCHAR(255) UNIQUE NOT NULL,
    client_secret VARCHAR(255),
    redirect_uris TEXT,
    access_token_validity INTEGER DEFAULT 3600,
    refresh_token_validity INTEGER DEFAULT 86400,
    scopes VARCHAR(255) DEFAULT 'openid profile email',
    require_pkce BOOLEAN DEFAULT FALSE,
    signing_algorithm VARCHAR(20) DEFAULT 'RS256',
    jwks_url VARCHAR(2048),
    issuer VARCHAR(2048)
);

-- Create saml_providers table
CREATE TABLE IF NOT EXISTS saml_providers (
    provider_uuid UUID PRIMARY KEY REFERENCES providers(provider_uuid) ON DELETE CASCADE,
    acs_url VARCHAR(2048) NOT NULL,
    issuer VARCHAR(255) NOT NULL,
    sp_binding VARCHAR(50) DEFAULT 'redirect',
    audience VARCHAR(255),
    signing_kp TEXT,
    verification_kp TEXT,
    property_mappings JSONB
);

-- ========================================
-- APPLICATIONS TABLES
-- ========================================

-- Create applications table
CREATE TABLE IF NOT EXISTS applications (
    application_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    group_name VARCHAR(255),
    description TEXT,
    provider_uuid UUID REFERENCES providers(provider_uuid) ON DELETE SET NULL,
    policy_engine_mode VARCHAR(20) DEFAULT 'any',
    meta_launch_url VARCHAR(2048),
    meta_description TEXT,
    meta_publisher VARCHAR(255),
    meta_icon VARCHAR(2048),
    open_in_new_tab BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create application_entitlements table
CREATE TABLE IF NOT EXISTS application_entitlements (
    entitlement_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    application_uuid UUID NOT NULL REFERENCES applications(application_uuid) ON DELETE CASCADE,
    object_type VARCHAR(50) NOT NULL, -- "user" 或 "group"
    object_uuid UUID NOT NULL,
    attributes JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(application_uuid, object_type, object_uuid)
);

-- Create user_application_access table
CREATE TABLE IF NOT EXISTS user_application_access (
    access_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_uuid UUID NOT NULL,
    application_uuid UUID NOT NULL REFERENCES applications(application_uuid) ON DELETE CASCADE,
    last_access TIMESTAMP WITH TIME ZONE,
    access_count INTEGER DEFAULT 0,
    session_key VARCHAR(255),
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_uuid, application_uuid)
);

-- Create application_audit_logs table
CREATE TABLE IF NOT EXISTS application_audit_logs (
    log_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    application_uuid UUID NOT NULL REFERENCES applications(application_uuid) ON DELETE CASCADE,
    user_uuid UUID,
    event_type VARCHAR(100) NOT NULL,
    event_action VARCHAR(100) NOT NULL,
    details JSONB,
    client_ip INET,
    user_agent TEXT,
    success BOOLEAN,
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========================================
-- EVENTS TABLES
-- ========================================

-- Create events table
CREATE TABLE IF NOT EXISTS events (
    event_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    action VARCHAR(100) NOT NULL,
    message TEXT,
    level VARCHAR(20) DEFAULT 'info',
    user_uuid UUID,
    application_uuid UUID REFERENCES applications(application_uuid) ON DELETE SET NULL,
    flow_uuid UUID REFERENCES flows(flow_uuid) ON DELETE SET NULL,
    stage_uuid UUID REFERENCES stages(stage_uuid) ON DELETE SET NULL,
    provider_uuid UUID REFERENCES providers(provider_uuid) ON DELETE SET NULL,
    source_uuid UUID REFERENCES sources(source_uuid) ON DELETE SET NULL,
    client_ip INET,
    user_agent TEXT,
    context JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Create event_notifications table
CREATE TABLE IF NOT EXISTS event_notifications (
    notification_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_uuid UUID NOT NULL REFERENCES events(event_uuid) ON DELETE CASCADE,
    target_type VARCHAR(50) NOT NULL,
    target_uuid UUID NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    sent_at TIMESTAMP WITH TIME ZONE,
    failed_at TIMESTAMP WITH TIME ZONE,
    error_msg TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create event_rules table
CREATE TABLE IF NOT EXISTS event_rules (
    rule_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    actions TEXT[],
    levels TEXT[],
    conditions JSONB,
    action_type VARCHAR(50) NOT NULL,
    action_config JSONB,
    rate_limit INTEGER DEFAULT 0,
    last_triggered TIMESTAMP WITH TIME ZONE,
    trigger_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========================================
-- INDEXES
-- ========================================

-- Sources indexes
CREATE INDEX IF NOT EXISTS idx_sources_slug ON sources(slug);
CREATE INDEX IF NOT EXISTS idx_sources_type ON sources(source_type);
CREATE INDEX IF NOT EXISTS idx_user_source_connections_user ON user_source_connections(user_uuid);
CREATE INDEX IF NOT EXISTS idx_user_source_connections_source ON user_source_connections(source_uuid);

-- Providers indexes
CREATE INDEX IF NOT EXISTS idx_providers_name ON providers(name);
CREATE INDEX IF NOT EXISTS idx_providers_type ON providers(provider_type);
CREATE INDEX IF NOT EXISTS idx_oauth2_providers_client_id ON oauth2_providers(client_id);

-- Applications indexes
CREATE INDEX IF NOT EXISTS idx_applications_slug ON applications(slug);
CREATE INDEX IF NOT EXISTS idx_applications_provider ON applications(provider_uuid);
CREATE INDEX IF NOT EXISTS idx_application_entitlements_app ON application_entitlements(application_uuid);
CREATE INDEX IF NOT EXISTS idx_application_entitlements_object ON application_entitlements(object_type, object_uuid);
CREATE INDEX IF NOT EXISTS idx_user_application_access_user ON user_application_access(user_uuid);
CREATE INDEX IF NOT EXISTS idx_user_application_access_app ON user_application_access(application_uuid);

-- Events indexes
CREATE INDEX IF NOT EXISTS idx_events_action ON events(action);
CREATE INDEX IF NOT EXISTS idx_events_level ON events(level);
CREATE INDEX IF NOT EXISTS idx_events_user ON events(user_uuid);
CREATE INDEX IF NOT EXISTS idx_events_created_at ON events(created_at);
CREATE INDEX IF NOT EXISTS idx_events_expires_at ON events(expires_at);
CREATE INDEX IF NOT EXISTS idx_event_notifications_event ON event_notifications(event_uuid);
CREATE INDEX IF NOT EXISTS idx_event_notifications_target ON event_notifications(target_type, target_uuid);

-- ========================================
-- TRIGGERS
-- ========================================

-- Create triggers for updated_at
CREATE TRIGGER update_sources_updated_at BEFORE UPDATE ON sources
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_source_connections_updated_at BEFORE UPDATE ON user_source_connections
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_providers_updated_at BEFORE UPDATE ON providers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_applications_updated_at BEFORE UPDATE ON applications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_application_entitlements_updated_at BEFORE UPDATE ON application_entitlements
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_application_access_updated_at BEFORE UPDATE ON user_application_access
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_event_notifications_updated_at BEFORE UPDATE ON event_notifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_event_rules_updated_at BEFORE UPDATE ON event_rules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
